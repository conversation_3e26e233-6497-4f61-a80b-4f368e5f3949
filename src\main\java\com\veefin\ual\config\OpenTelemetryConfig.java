package com.veefin.ual.config;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.propagation.W3CTraceContextPropagator;
import io.opentelemetry.context.propagation.ContextPropagators;
import io.opentelemetry.exporter.otlp.http.trace.OtlpHttpSpanExporter;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.resources.Resource;
import io.opentelemetry.sdk.trace.SdkTracerProvider;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor;
import io.opentelemetry.sdk.trace.samplers.Sampler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for OpenTelemetry integration.
 * This sets up the OpenTelemetry SDK with appropriate configuration for tracing.
 */
@Configuration
public class OpenTelemetryConfig {

    private static final Logger logger = LoggerFactory.getLogger(OpenTelemetryConfig.class);

    @Value("${spring.application.name}")
    private String serviceName;

    @Value("${otel.exporter.otlp.traces.endpoint:https://signoz-ingest.veefin.in/v1/traces}")
    private String tracesEndpoint;

    /**
     * Creates and configures the OpenTelemetry instance for the application.
     *
     * @return Configured OpenTelemetry instance
     */
    @Bean
    public OpenTelemetry openTelemetry() {
        logger.info("Configuring OpenTelemetry with traces endpoint: {}", tracesEndpoint);

        // Create a resource with service name attribute
        Resource resource = Resource.getDefault()
                .merge(Resource.create(Attributes.of(
                        AttributeKey.stringKey("service.name"), serviceName)));

        // Create the OTLP HTTP Exporter for SigNoz
        OtlpHttpSpanExporter spanExporter = OtlpHttpSpanExporter.builder()
                .setEndpoint(tracesEndpoint)
                .build();

        logger.info("Configured OTLP exporter with endpoint: {}", tracesEndpoint);

        // Configure the tracer provider with the exporter
        SdkTracerProvider sdkTracerProvider = SdkTracerProvider.builder()
                .setResource(resource)
                .setSampler(Sampler.alwaysOn())
                .addSpanProcessor(BatchSpanProcessor.builder(spanExporter).build())
                .build();

        // Build the OpenTelemetry SDK
        OpenTelemetrySdk openTelemetrySdk = OpenTelemetrySdk.builder()
                .setTracerProvider(sdkTracerProvider)
                .setPropagators(ContextPropagators.create(W3CTraceContextPropagator.getInstance()))
                .build();

        // Add shutdown hook to close the tracer provider
        Runtime.getRuntime().addShutdownHook(new Thread(sdkTracerProvider::close));

        logger.info("OpenTelemetry configured successfully");
        return openTelemetrySdk;
    }
}
