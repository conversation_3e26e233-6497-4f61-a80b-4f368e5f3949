package com.veefin.ual.controller;

import java.util.concurrent.CompletableFuture;

import org.apache.coyote.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.veefin.ual.dto.EventSearchRequestDTO;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.SessionSearchRequestDTO;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.exceptionhandler.ResourceNotFound;
import com.veefin.ual.service.EventSearchService;
import com.veefin.ual.service.UserActivitySessionService;
import com.veefin.ual.utilities.UserActivitySessionUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

@RestController
@RequestMapping("v1")
@Tag(name = "User Activity Controller", description = "APIs for user activity sessions and events")
public class UserActivityLogController {

	private final UserActivitySessionService userActivitySessionService;
	private final EventSearchService eventSearchService;

	public UserActivityLogController(UserActivitySessionService userActivitySessionService,
			EventSearchService eventSearchService) {
		this.userActivitySessionService = userActivitySessionService;
		this.eventSearchService = eventSearchService;
	}

	Logger logger = LoggerFactory.getLogger(UserActivityLogController.class);

	@Operation(summary = "Identify user session", description = """
			Registers a new user session.

			- **system_name** is required and should be one of: `SCF`, `CASH`, or `TRADE`.
			- The JWT token must be passed in the header and is validated against Redis using the session ID.
			- On success, returns a `user_activity_session_id`, which must be used in subsequent API calls.
			- On error, returns `ERROR_CODE` and an appropriate message.
			""")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Session registered successfully", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class), examples = @io.swagger.v3.oas.annotations.media.ExampleObject(value = "{\"code\":\"200\",\"message\":\"Session registered successfully\",\"data\":\"user_activity_session_id_value\"}"))),
			@ApiResponse(responseCode = "400", description = "Invalid request data or missing system_name", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))),
			@ApiResponse(responseCode = "401", description = "Unauthorized - Invalid or expired token", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))),
			@ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))) })
	@PostMapping("identify")
	public ResponseEntity<ResponseDTO<?>> addUserActivitySession(
			@Valid @RequestBody UserActivitySessionDTO userActivitySessionDTO, HttpServletRequest request)
			throws ResourceNotFound {

		logger.debug("addUserActivitySession api starts");
		UserActivitySessionUtil.getFieldFromToken(userActivitySessionDTO, request);
		ResponseDTO<?> responseDTO = userActivitySessionService.addUserActivitySession(userActivitySessionDTO);
		logger.debug("addUserActivitySession api ends");
		return new ResponseEntity<>(responseDTO, HttpStatusCode.valueOf(HttpStatus.OK.value()));
	}

	@Operation(summary = "Advanced search for user events", description = """
				Returns a paginated list of user events with enhanced filtering options:

				* Filter by session ID, module, action, system name
				* Search by user name or email or action or module or userAgent or clientIp
				* Support for exact matching of user information
				* Multi-term search with configurable matching logic
				* Date range filtering

				The response includes user information (name, email, UUID) from the associated session.
			""")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Paginated list of events with user information", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class), examples = @io.swagger.v3.oas.annotations.media.ExampleObject(value = "{\"code\":\"200\",\"message\":\"Events retrieved successfully\",\"data\":{\"results\":[{\"id\":\"event_id\",\"module\":\"module_name\",\"action\":\"action_name\",\"user_activity_sessionId\":\"session_id\",\"system_name\":\"SCF\",\"properties\":\"{...}\",\"client_ip\":\"127.0.0.1\",\"user_agent\":\"Mozilla/5.0\",\"event_at\":\"01-01-2023 12:00:00\",\"message\":\"Event message\",\"user_name\":\"John Doe\",\"user_email\":\"<EMAIL>\",\"user_uuid\":\"user_uuid\"}],\"total_count\":100}}"))),
			@ApiResponse(responseCode = "400", description = "Invalid request parameters", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))),
			@ApiResponse(responseCode = "500", description = "Server error", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))) })
	@PostMapping("events/search")
	public ResponseEntity<ResponseDTO<?>> searchEvents(
			@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Search criteria and pagination parameters", content = @Content(schema = @Schema(implementation = EventSearchRequestDTO.class))) @RequestBody EventSearchRequestDTO requestDto) {
		logger.debug("searchEvents API start");
		ResponseDTO<?> paginatedResult = eventSearchService.searchEvents(requestDto);
		logger.debug("searchEvents API ends");
		return new ResponseEntity<>(paginatedResult, HttpStatus.OK);
	}

	@Operation(summary = "Pagination for user session", description = """
			Retrieves paginated list of user activity sessions filtered by search field:
			- id
			- name
			- email
			- user_uuid
			- systemName
			- userAgent
			- clientIp

			The filter and pagination details should be passed in `PaginationRequestDTO`.
			""")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successfully fetched paginated user sessions", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class), examples = @io.swagger.v3.oas.annotations.media.ExampleObject(value = "{\"code\":\"200\",\"message\":\"User sessions retrieved successfully\",\"data\":{\"total_count\":100,\"sessions\":[{\"id\":\"session_id\",\"name\":\"John Doe\",\"email\":\"<EMAIL>\",\"user_uuid\":\"user_uuid\",\"system_name\":\"SCF\",\"user_agent\":\"Mozilla/5.0\",\"client_ip\":\"127.0.0.1\",\"source\":\"WEB\"}]}}"))),
			@ApiResponse(responseCode = "400", description = "Invalid filter or pagination data", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))),
			@ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))) })
	@PostMapping("user-activity-sessions/search")
	public ResponseEntity<ResponseDTO<?>> getUserSessions(@RequestBody SessionSearchRequestDTO requestDto) {
		logger.debug("getUserSessions API start");

		ResponseDTO<?> paginatedResult = userActivitySessionService.getUserSessions(requestDto);

		logger.debug("getUserSessions API ends");
		return new ResponseEntity<>(paginatedResult, HttpStatus.OK);
	}

	@Operation(summary = "Process events asynchronously", description = """
			Processes user events asynchronously using virtual threads.
			- Requires valid `user_activity_session_id`.
			- `system_name` is required and derived from the JWT token.
			- The JWT token must be passed in the Authorization header and is validated against Redis.
			- This endpoint demonstrates the use of virtual threads for asynchronous processing.
			""")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "202", description = "Event processing started asynchronously", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class), examples = @io.swagger.v3.oas.annotations.media.ExampleObject(value = "{\"code\":\"202\",\"message\":\"Event processing started asynchronously\",\"data\":\"async_event_id_value\"}"))),
			@ApiResponse(responseCode = "400", description = "Bad request or missing fields", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))),
			@ApiResponse(responseCode = "401", description = "Unauthorized - Invalid or missing JWT token", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))),
			@ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseDTO.class))) })
	@PostMapping("events")
	public CompletableFuture<ResponseEntity<ResponseDTO<?>>> processEventsAsync(
			@Valid @RequestBody UserActivityEventsDTO userActivityEventsDTO, HttpServletRequest request)
			throws BadRequestException {
		logger.debug("processEventsAsync api starts");
		UserActivitySessionUtil.getFieldFromTokenForEvents(userActivityEventsDTO, request);

		// This will be processed in a virtual thread
		return userActivitySessionService.processEventsAsync(userActivityEventsDTO).thenApply(responseDTO -> {
			logger.debug("processEventsAsync api completed asynchronously");
			return new ResponseEntity<>(responseDTO, HttpStatus.ACCEPTED);
		});
	}

}
