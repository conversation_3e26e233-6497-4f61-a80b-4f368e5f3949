# OpenTelemetry and Sentry Integration for User Activity Logger

This document explains how the OpenTelemetry and Sentry integrations are set up in the user-activity-logger project.

## Overview

### OpenTelemetry

OpenTelemetry is integrated into the user-activity-logger to provide distributed tracing capabilities. The integration is configured to send traces to SigNoz at `https://signoz-ingest.veefin.in/v1/traces`.

### Sentry

Sentry is integrated for error tracking and monitoring. It captures exceptions and errors that occur in the application and sends them to the Sentry server for analysis. The integration is configured to work with OpenTelemetry, allowing for correlation between traces and errors.

## Configuration

### OpenTelemetry Configuration

The OpenTelemetry configuration is set up in the following files:

1. **pom.xml**: Contains the OpenTelemetry dependencies
   - `opentelemetry-api` and `opentelemetry-sdk`: Core OpenTelemetry libraries
   - `opentelemetry-spring-boot-starter`: Spring Boot integration
   - `opentelemetry-log4j-context-data-2.17-autoconfigure`: Log4j2 context data integration
   - `opentelemetry-log4j-appender-2.17`: Log4j2 appender for OpenTelemetry
   - `opentelemetry-semconv`: Semantic conventions for OpenTelemetry

2. **application.properties**: Contains the OpenTelemetry configuration properties
   ```properties
   otel.service.name=${spring.application.name}
   otel.traces.exporter=otlp
   otel.metrics.exporter=otlp
   otel.logs.exporter=otlp
   otel.exporter.otlp.endpoint=${OTEL_EXPORTER_OTLP_ENDPOINT:https://signoz-ingest.veefin.in}
   otel.exporter.otlp.traces.endpoint=${OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:https://signoz-ingest.veefin.in/v1/traces}
   otel.exporter.otlp.protocol=http/protobuf
   otel.propagators=tracecontext,baggage
   otel.traces.sampler=always_on
   ```

3. **log4j2.xml**: Contains the Log4j2 configuration with OpenTelemetry appender
   ```xml
   <!-- OpenTelemetry Appender -->
   <OpenTelemetry name="OpenTelemetryAppender">
       <captureExperimentalAttributes>true</captureExperimentalAttributes>
       <captureCodeAttributes>true</captureCodeAttributes>
       <captureMarkerAttributes>true</captureMarkerAttributes>
       <captureContextDataAttributes>true</captureContextDataAttributes>
       <captureMapMessageAttributes>true</captureMapMessageAttributes>
   </OpenTelemetry>
   ```

4. **OpenTelemetryConfig.java**: Contains the Java configuration for OpenTelemetry
5. **OpenTelemetryInterceptor.java**: Contains the HTTP interceptor for trace context propagation
6. **TraceContextUtils.java**: Contains utility methods for accessing trace context

### Sentry Configuration

The Sentry configuration is set up in the following files:

1. **pom.xml**: Contains the Sentry dependencies
   - `sentry-spring-boot-starter-jakarta`: Spring Boot integration for Sentry
   - `sentry-log4j2`: Log4j2 integration for Sentry

2. **application.properties**: Contains the Sentry configuration properties
   ```properties
   sentry.dsn=${SENTRY_DSN:https://<EMAIL>/19}
   sentry.traces-sample-rate=${SENTRY_RATE:1.0}
   sentry.attach-stacktrace=${SENTRY_TRACE:true}
   sentry.environment=${spring.profiles.active:local}
   sentry.release=${spring.application.name}@1.0.0
   sentry.in-app-includes=com.veefin.ual
   sentry.send-default-pii=true
   ```

3. **log4j2.xml**: Contains the Log4j2 configuration with Sentry appender
   ```xml
   <!-- Sentry Appender -->
   <Sentry name="Sentry">
       <minimumEventLevel>ERROR</minimumEventLevel>
       <minimumBreadcrumbLevel>INFO</minimumBreadcrumbLevel>
   </Sentry>
   ```

4. **SentryConfig.java**: Contains the Java configuration for Sentry integration with OpenTelemetry
   ```java
   @EventListener(ApplicationReadyEvent.class)
   public void configureSentry() {
       // Add OpenTelemetry integration to Sentry
       io.sentry.Sentry.configureScope(scope -> {
           scope.addEventProcessor(new OpenTelemetryEventProcessor());
       });
   }
   ```
5. **GlobalExceptionHandler.java**: Contains exception handling with Sentry integration

## Usage

### Accessing Trace Context

You can access the current trace and span IDs using the `TraceContextUtils` class:

```java
import com.veefin.ual.util.TraceContextUtils;

// Get the current trace ID
String traceId = TraceContextUtils.getTraceId();

// Get the current span ID
String spanId = TraceContextUtils.getSpanId();
```

### Creating Custom Spans

You can create custom spans using the OpenTelemetry API:

```java
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;

// Inject the tracer
@Autowired
private Tracer tracer;

// Create a span
Span span = tracer.spanBuilder("operation-name").startSpan();

// Make the span the current span
try (Scope scope = span.makeCurrent()) {
    // Your code here

    // Add attributes to the span
    span.setAttribute("attribute-key", "attribute-value");

    // Record events
    span.addEvent("event-name");
} finally {
    // End the span
    span.end();
}
```

### Testing the Integration

You can test the OpenTelemetry integration by making any API request to the application. All requests will automatically be traced, and you can view the traces in the SigNoz UI.

## Viewing Traces

Traces are sent to SigNoz at `https://signoz-ingest.veefin.in/v1/traces`. You can view the traces in the SigNoz UI.

## Viewing Errors in Sentry

Errors and exceptions are sent to Sentry. You can view them in the Sentry UI by accessing your Sentry project dashboard.

The integration includes:
- Automatic capture of unhandled exceptions
- Manual capture of exceptions in the GlobalExceptionHandler
- Correlation between Sentry events and OpenTelemetry traces
- Breadcrumbs for tracking application flow

## Customizing the Configuration

### OpenTelemetry Configuration

You can customize the OpenTelemetry configuration by modifying the properties in `application.properties` or by providing environment variables:

```properties
# OpenTelemetry Configuration
otel.service.name=user-activity-logger
otel.traces.exporter=otlp
otel.metrics.exporter=otlp
otel.logs.exporter=otlp
otel.exporter.otlp.endpoint=https://signoz-ingest.veefin.in
otel.exporter.otlp.traces.endpoint=https://signoz-ingest.veefin.in/v1/traces
otel.exporter.otlp.protocol=http/protobuf
```

### Sentry Configuration

You can customize the Sentry configuration by modifying the properties in `application.properties` or by providing environment variables:

```properties
# Sentry Configuration
sentry.dsn=${SENTRY_DSN:https://<EMAIL>/19}
sentry.traces-sample-rate=${SENTRY_RATE:1.0}
sentry.attach-stacktrace=${SENTRY_TRACE:true}
```

## Troubleshooting

### OpenTelemetry Troubleshooting

If you encounter issues with the OpenTelemetry integration, check the following:

1. Ensure the SigNoz endpoint is accessible from your environment
2. Check the application logs for any OpenTelemetry-related errors
3. Verify that the OpenTelemetry dependencies are correctly included in the project

### Sentry Troubleshooting

If you encounter issues with the Sentry integration, check the following:

1. Ensure the Sentry DSN is correct and accessible from your environment
2. Check the application logs for any Sentry-related errors
3. Verify that the Sentry dependencies are correctly included in the project
4. Check that the SentryConfig class is properly configured
