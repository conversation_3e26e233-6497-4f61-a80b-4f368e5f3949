echo "Setting environment variables..."
DEV_VERSION='*******-DEV'
STAGING_VERSION='*******-STAGING'
BITBUCKET_COMMIT_SHORT="${BITBUCKET_COMMIT::7}"
MASTER_VERSION='*******'
MAVEN_FTP_USER='ghactions'
MAVEN_BASE_PATH='/mount/veefinmaven/repository'  # Changed back to /mount/veefinmaven/repository
FOLDER_BASE_PATH='/com/veefin/'
VERSION_TYPE=''
VERSION_SUFFIX='-DEV'
MAVEN_COM_VEEFIN_DIR='/home/<USER>/.m2/repository/com/veefin'

echo "MAVEN_FTP_USER: ${MAVEN_FTP_USER}"
echo "MAVEN_FTP_HOST: ${MAVEN_FTP_HOST}"
echo "MAVEN_BASE_PATH: ${MAVEN_BASE_PATH}"
echo "BITBUCKET_BRANCH=${BITBUCKET_BRANCH}"
echo "FOLDER_BASE_PATH=${FOLDER_BASE_PATH}"

# Extract the version from pom.xml
MVN_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
echo "Extracted version from pom.xml: ${MVN_VERSION}"

# Set version based on branch
echo "Building the Maven project..."
if [ ${BITBUCKET_BRANCH} == staging ]; then VERSION_TYPE=$STAGING_VERSION; VERSION_SUFFIX='-QA'; fi
if [ ${BITBUCKET_BRANCH} == master ]; then VERSION_TYPE=$MASTER_VERSION; VERSION_SUFFIX=''; fi
if [ ${BITBUCKET_BRANCH} == development ]; then VERSION_TYPE=$DEV_VERSION; VERSION_SUFFIX='-DEV'; fi

# Set Maven properties for versions
mvn versions:set-property -Dproperty=securities.version -DnewVersion=${VERSION_TYPE} -DprocessAllModules

# Deleting all local com.veefin files
rm -rf ${MAVEN_COM_VEEFIN_DIR}

# Run Maven build
mvn clean install -Dmaven.test.skip=true -Denv.suffix=${VERSION_SUFFIX}

echo "Uploading folder..."
echo "FOLDER_BASE_PATH=${FOLDER_BASE_PATH}"

# Rsync operation only if version is set
if [ ! -z "$VERSION_TYPE" ]; then
    # Upload the user-activity-logger folder
    rsync -azr -e "ssh -o StrictHostKeyChecking=no" ~/.m2/repository${FOLDER_BASE_PATH}/user-activity-logger ${MAVEN_FTP_USER}@${MAVEN_FTP_HOST}:${MAVEN_BASE_PATH}/core${FOLDER_BASE_PATH} --rsync-path="mkdir -p ${MAVEN_BASE_PATH}/core${FOLDER_BASE_PATH} && rsync"

    # Use the correct version from pom.xml for the file path
    rsync -azr ${MAVEN_COM_VEEFIN_DIR}/user-activity-logger/${MVN_VERSION}/user-activity-logger-${MVN_VERSION}.jar ${MAVEN_FTP_USER}@${MAVEN_FTP_HOST}:${MAVEN_BASE_PATH}/core/com/veefin/user-activity-logger/${MVN_VERSION}/user-activity-logger-${MVN_VERSION}.jar.$BITBUCKET_COMMIT_SHORT
else
    echo "VERSION_TYPE is empty. Skipping rsync step."
fi