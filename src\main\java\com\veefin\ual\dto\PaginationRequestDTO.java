package com.veefin.ual.dto;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PaginationRequestDTO {
	public String search;
	public LocalDate to;
	public LocalDate from;

	@JsonProperty("system_name")
	public String systemName;
	public Integer page = 0;
	public Integer size = 10;
	
	private String module;
	
	private String action;

	@JsonProperty("user_agent")
	private String userAgent;

	@JsonProperty("user_activity_session_id")
	private String userActivitySessionId;

	@JsonProperty("user_id")
	private String userUuid;

	public String getUserUuid() {
		return userUuid;
	}

	public void setUserUuid(String userUuid) {
		this.userUuid = userUuid;
	}

	public String getSearch() {
		return search;
	}

	public void setSearch(String search) {
		this.search = search;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getSize() {
		return size;
	}

	public void setSize(Integer size) {
		this.size = size;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public LocalDate getTo() {
		return to;
	}

	public void setTo(LocalDate to) {
		this.to = to;
	}

	public LocalDate getFrom() {
		return from;
	}

	public void setFrom(LocalDate from) {
		this.from = from;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getUserActivitySessionId() {
		return userActivitySessionId;
	}

	public void setUserActivitySessionId(String userActivitySessionId) {
		this.userActivitySessionId = userActivitySessionId;
	}
}
