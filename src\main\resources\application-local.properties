spring.application.name=ual
server.servlet.context-path=/ual
server.port=7082

## Database configuration ##
spring.datasource.url=***************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.jpa.hibernate.ddl-auto=update
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=true

## Flyway configuration ##
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration
spring.flyway.table=flyway_schema_history
spring.flyway.baseline-version=0
spring.flyway.validate-on-migrate=false
spring.flyway.repair-on-migrate=true
spring.flyway.repair=true
spring.flyway.out-of-order=true

## RabbitMQ configuration ##
rabbitmq.queue.name=event-message-queue
rabbitmq.exchange.name=event-message-exchange
rabbitmq.routing.key=event_message
add.event.db=false

## Spring Redis configuration ##
spring.redis.host=localhost
spring.redis.port=6379
SECURITY_REDIS_HOST=localhost
SECURITY_REDIS_PORT=6379


## JWT Authentication Keys ##
jwt.rsa-private-key=certs/privateKey.pem
jwt.rsa-public-key=certs/publicKey.pem

oauth.server.issuer=oauth

sentry.enabled=${SENTRY_ENABLED:true}
sentry.dsn=${SENTRY_DSN:https://<EMAIL>/19}
sentry.traces-sample-rate=1.0
sentry.attach-stacktrace=${SENTRY_TRACE:true}
sentry.environment=local
sentry.release=${spring.application.name}@1.0.0
sentry.in-app-includes=com.veefin.ual
sentry.send-default-pii=true

## RabbitMQ configuration ##
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest

## Logging configuration ##
logging.level.root=INFO
logging.level.com.veefin.ual.controller=DEBUG
logging.level.com.veefin.ual.service=DEBUG
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO
logging.level.io.opentelemetry=INFO

## OpenTelemetry Configuration for Local Environment ##
otel.service.name=user-activity-logger
otel.traces.exporter=otlp
otel.metrics.exporter=otlp
otel.logs.exporter=otlp
otel.exporter.otlp.endpoint=https://signoz-ingest.veefin.in
otel.exporter.otlp.traces.endpoint=https://signoz-ingest.veefin.in/v1/traces
otel.propagators=tracecontext,baggage
otel.traces.sampler=always_on
otel.exporter.otlp.protocol=http/protobuf

## Actuator configuration ##
# Local-specific Actuator settings can be added here if needed
# The main configuration is in application.properties
# These settings will override the main configuration when the local profile is active
info.app.environment=local
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.prometheus.metrics.export.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.slo.http.server.requests=1ms,5ms,10ms,50ms,100ms,200ms,500ms,1s,5s
management.metrics.tags.application=${spring.application.name}
