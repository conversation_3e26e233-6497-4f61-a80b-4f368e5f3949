package com.veefin.ual.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.veefin.ual.config.TestConfig;
import com.veefin.ual.dto.EventSearchRequestDTO;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.SessionSearchRequestDTO;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.service.EventSearchService;
import com.veefin.ual.service.UserActivitySessionService;
import com.veefin.ual.utilities.UserActivitySessionUtil;
import org.mockito.ArgumentMatchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(UserActivityLogController.class)
@ActiveProfiles("test")
public class UserActivityLogControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private UserActivitySessionService userActivitySessionService;

    @MockBean
    private EventSearchService eventSearchService;

    private UserActivitySessionDTO userActivitySessionDTO;
    private UserActivityEventsDTO userActivityEventsDTO;
    private EventSearchRequestDTO eventSearchRequestDTO;
    private SessionSearchRequestDTO sessionSearchRequestDTO;

    @BeforeEach
    void setUp() {
        // Initialize test data
        userActivitySessionDTO = new UserActivitySessionDTO();
        userActivitySessionDTO.setEmail("<EMAIL>");
        userActivitySessionDTO.setName("Test User");
        userActivitySessionDTO.setUuid("test-uuid");
        userActivitySessionDTO.setSystemName("TEST");
        userActivitySessionDTO.setUserAgent("Test Agent");
        userActivitySessionDTO.setClientIp("127.0.0.1");
        userActivitySessionDTO.setSource("WEB");

        userActivityEventsDTO = new UserActivityEventsDTO();
        userActivityEventsDTO.setUserActivitySessionId("test-session-id");
        userActivityEventsDTO.setModule("TEST_MODULE");
        userActivityEventsDTO.setAction("TEST_ACTION");
        userActivityEventsDTO.setSystemName("TEST");

        eventSearchRequestDTO = new EventSearchRequestDTO();
        eventSearchRequestDTO.setPage(0);
        eventSearchRequestDTO.setSize(10);
        eventSearchRequestDTO.setModule("TEST_MODULE");

        sessionSearchRequestDTO = new SessionSearchRequestDTO();
        sessionSearchRequestDTO.setPage(0);
        sessionSearchRequestDTO.setSize(10);
        sessionSearchRequestDTO.setSystemName("TEST");
    }

    @Test
    void testAddUserActivitySession() throws Exception {
        // Mock the static method
        try (MockedStatic<UserActivitySessionUtil> mockedStatic = mockStatic(UserActivitySessionUtil.class)) {
            // Mock the service response
            ResponseDTO<String> responseDTO = new ResponseDTO<>(HttpStatus.OK.toString(), "Session created successfully", "test-session-id");
            when(userActivitySessionService.addUserActivitySession(any(UserActivitySessionDTO.class))).thenReturn((ResponseDTO) responseDTO);

            // Perform the request
            mockMvc.perform(post("/v1/identify")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(userActivitySessionDTO)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(HttpStatus.OK.toString()))
                    .andExpect(jsonPath("$.message").value("Session created successfully"))
                    .andExpect(jsonPath("$.data").value("test-session-id"));

            // Verify service was called
            verify(userActivitySessionService, times(1)).addUserActivitySession(any(UserActivitySessionDTO.class));
        }
    }

    @Test
    void testProcessEvents() throws Exception {
        // Mock the service response
        ResponseDTO<Object> responseDTO = new ResponseDTO<>(HttpStatus.ACCEPTED.toString(), "Events processed successfully", null);
        when(userActivitySessionService.processEventsAsync(any(UserActivityEventsDTO.class)))
                .thenReturn(CompletableFuture.completedFuture((ResponseDTO) responseDTO));

        // Perform the request
        mockMvc.perform(post("/v1/events")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(userActivityEventsDTO)))
                .andExpect(status().isAccepted())
                .andExpect(jsonPath("$.code").value(HttpStatus.ACCEPTED.toString()))
                .andExpect(jsonPath("$.message").value("Events processed successfully"));

        // Verify service was called
        verify(userActivitySessionService, times(1)).processEventsAsync(any(UserActivityEventsDTO.class));
    }

    @Test
    void testSearchEvents() throws Exception {
        // Mock the service response
        ResponseDTO<Object> responseDTO = new ResponseDTO<>(HttpStatus.OK.toString(), "Events retrieved successfully", null);
        when(eventSearchService.searchEvents(any(EventSearchRequestDTO.class))).thenReturn((ResponseDTO) responseDTO);

        // Perform the request
        mockMvc.perform(post("/v1/events/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(eventSearchRequestDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(HttpStatus.OK.toString()))
                .andExpect(jsonPath("$.message").value("Events retrieved successfully"));

        // Verify service was called
        verify(eventSearchService, times(1)).searchEvents(any(EventSearchRequestDTO.class));
    }

    @Test
    void testSearchUserSessions() throws Exception {
        // Mock the service response
        ResponseDTO<Object> responseDTO = new ResponseDTO<>(HttpStatus.OK.toString(), "Sessions retrieved successfully", null);
        when(userActivitySessionService.getUserSessions(any(SessionSearchRequestDTO.class))).thenReturn((ResponseDTO) responseDTO);

        // Perform the request
        mockMvc.perform(post("/v1/user-activity-sessions/search")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sessionSearchRequestDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(HttpStatus.OK.toString()))
                .andExpect(jsonPath("$.message").value("Sessions retrieved successfully"));

        // Verify service was called
        verify(userActivitySessionService, times(1)).getUserSessions(any(SessionSearchRequestDTO.class));
    }
}
