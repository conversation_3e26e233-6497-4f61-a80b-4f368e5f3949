# ---------------------------
# Build stage
# ---------------------------
FROM registry.access.redhat.com/ubi9/ubi AS build
ENV APP_DIR=/app \
    JAVA_HOME=/usr/lib/jvm/java-21-openjdk

# Set the working directory inside the container
WORKDIR $APP_DIR

# Copy the project's Maven configuration (pom.xml)
COPY user-activity-logger/pom.xml .

# Install Maven (not included in UBI 9 OpenJDK image)
RUN dnf install -y java-21-openjdk-devel maven

# Copy the project's source code
COPY user-activity-logger/src ./src

# Compile the Java application
RUN mvn clean package -Dmaven.compiler.release=21 -Denv.suffix=-DEV -DskipTests=true

# ---------------------------
# Production stage
# ---------------------------
FROM registry.access.redhat.com/ubi9/ubi AS production
ENV APP_DIR=/app \
    JAVA_HOME=/usr/lib/jvm/java-21-openjdk

RUN dnf install -y java-21-openjdk-devel

# Non-root user
USER 1001

# Set working directory
WORKDIR $APP_DIR

# Copy Build to Final Image
COPY --from=build $APP_DIR/target/user-activity-logger-0.0.1-DEV.jar .

# Expose the port your Remix app will run on
EXPOSE 8092
EXPOSE 9092

# Define the command to run your application (Replace with your main class)
CMD ["java", "-jar","-DpropertyPath=/app" ,"/app/user-activity-logger-0.0.1-DEV.jar"] 
#CMD ["sleep", "10000"]