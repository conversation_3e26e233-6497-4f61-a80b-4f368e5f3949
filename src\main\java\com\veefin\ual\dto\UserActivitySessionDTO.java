package com.veefin.ual.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

public class UserActivitySessionDTO {
	@JsonIgnore
	@Schema(hidden = true)
	private String name;
	private String email;
	private String uuid;
	@NotBlank(message = "App Name is required")
	@JsonProperty("system_name")
	@JsonAlias({"systemName", "system_name"})
	private String systemName;
	@JsonProperty("user_agent")
	@JsonAlias({"userAgent", "user_agent"})
	private String userAgent;
	@JsonProperty("client_ip")
	@JsonAlias({"clientIp", "client_ip"})
	private String clientIp;

	private String source;

	public UserActivitySessionDTO(String name, String email, String uuid, String systemName, String userAgent,
			String clientIp, String source) {
		this.name = name;
		this.email = email;
		this.uuid = uuid;
		this.systemName = systemName;
		this.userAgent = userAgent;
		this.clientIp = clientIp;
		this.source = source;
	}

	public UserActivitySessionDTO() {
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
}
