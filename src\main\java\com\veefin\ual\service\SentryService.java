package com.veefin.ual.service;

import java.util.Map;
import java.util.function.Consumer;

/**
 * Service interface for Sentry operations.
 * Provides abstraction over Sentry functionality to handle cases where Sentry might not be available.
 */
public interface SentryService {

    /**
     * Captures an exception and sends it to Sentry if available.
     * If Sentry is not available, logs the exception instead.
     *
     * @param throwable the exception to capture
     */
    void captureException(Throwable throwable);

    /**
     * Captures a message and sends it to Sentry if available.
     * If <PERSON><PERSON> is not available, logs the message instead.
     *
     * @param message the message to capture
     */
    void captureMessage(String message);

    /**
     * Captures a message with additional context and sends it to Sentry if available.
     * If Sen<PERSON> is not available, logs the message and context instead.
     *
     * @param message the message to capture
     * @param extras additional context data
     */
    void captureMessage(String message, Map<String, String> extras);

    /**
     * Executes a scope operation with <PERSON><PERSON> if available.
     * If Sentry is not available, the operation is skipped.
     *
     * @param scopeCallback the callback to execute with Sentry scope
     */
    void withScope(Consumer<SentryScope> scopeCallback);

    /**
     * Checks if Sen<PERSON> is available and properly configured.
     *
     * @return true if <PERSON><PERSON> is available, false otherwise
     */
    boolean isAvailable();

    /**
     * Interface representing Sentry scope operations.
     * This abstraction allows us to provide a fallback when Sentry is not available.
     */
    interface SentryScope {
        /**
         * Sets extra data in the scope.
         *
         * @param key the key for the extra data
         * @param value the value for the extra data
         */
        void setExtra(String key, String value);

        /**
         * Captures a message within this scope.
         *
         * @param message the message to capture
         */
        void captureMessage(String message);
    }
}
