<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" shutdownHook="disable">
    <Properties>
        <!-- Use project-relative path for logs -->
        <Property name="LOG_PATH">./logs</Property>
        <Property name="log.pattern">%d{yyyy-MM-dd HH:mm:ss.SSS}{IST} [%t] [%X{trace_id},%X{span_id}] %-5level %logger{36}.%M - %msg%n%throwable</Property>
        <Property name="SERVICE_NAME">user-activity-logger</Property>
    </Properties>

    <Appenders>
        <!-- Console Logger -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout>
                <Pattern>${log.pattern}</Pattern>
            </PatternLayout>
        </Console>

        <!-- Rolling File Logger for all logs -->
        <RollingFile name="AppLogger"
                     fileName="${LOG_PATH}/user-activity-logger.log"
                     filePattern="${LOG_PATH}/user-activity-logger-%d{yyyy-MM-dd}.log.gz">
            <PatternLayout>
                <Pattern>${log.pattern}</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Rolling File Logger for error logs -->
        <RollingFile name="ErrorLogger"
                     fileName="${LOG_PATH}/error/user-activity-logger-error.log"
                     filePattern="${LOG_PATH}/error/user-activity-logger-error-%d{yyyy-MM-dd}.log.gz">
            <PatternLayout>
                <Pattern>${log.pattern}</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingFile>

        <!-- Request/Response Logger with JSON Layout -->
        <RollingFile name="RequestLogger"
                     fileName="${LOG_PATH}/request/user-activity-logger-request.log"
                     filePattern="${LOG_PATH}/request/user-activity-logger-request-%d{yyyy-MM-dd}.log.gz">
            <JsonTemplateLayout eventTemplateUri="classpath:JsonLayout.json">
                <EventTemplateAdditionalField key="service.name" value="${SERVICE_NAME}" />
            </JsonTemplateLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>

        <!-- Async Wrapper for Request Logger -->
        <Async name="AsyncRequestLogger" bufferSize="512" includeLocation="true">
            <AppenderRef ref="RequestLogger"/>
        </Async>

        <!-- OpenTelemetry Appender -->
        <OpenTelemetry name="OpenTelemetryAppender">
            <captureExperimentalAttributes>true</captureExperimentalAttributes>
            <captureCodeAttributes>true</captureCodeAttributes>
            <captureMarkerAttributes>true</captureMarkerAttributes>
            <captureContextDataAttributes>true</captureContextDataAttributes>
            <captureMapMessageAttributes>true</captureMapMessageAttributes>
        </OpenTelemetry>

        <!-- Sentry Appender -->
        <Sentry name="Sentry">
            <minimumEventLevel>ERROR</minimumEventLevel>
            <minimumBreadcrumbLevel>INFO</minimumBreadcrumbLevel>
        </Sentry>
    </Appenders>

    <Loggers>
        <!-- Root logger sends to all appenders -->
        <AsyncRoot level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppLogger"/>
            <AppenderRef ref="ErrorLogger"/>
            <AppenderRef ref="OpenTelemetryAppender"/>
            <AppenderRef ref="Sentry"/>
        </AsyncRoot>

        <!-- OpenTelemetry Logger -->
        <Logger name="io.opentelemetry" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppLogger"/>
        </Logger>

        <!-- Request Logger -->
        <Logger name="request-logger" level="INFO" additivity="false">
            <AppenderRef ref="AsyncRequestLogger"/>
            <AppenderRef ref="Console"/>
        </Logger>
    </Loggers>
</Configuration>
