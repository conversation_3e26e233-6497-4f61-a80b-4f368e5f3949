package com.veefin.ual.util;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for accessing trace context information.
 */
public class TraceContextUtils {

    private static final Logger logger = LoggerFactory.getLogger(TraceContextUtils.class);

    private TraceContextUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Gets the current trace ID from the active span.
     *
     * @return The trace ID, or an empty string if no span is active
     */
    public static String getTraceId() {
        SpanContext spanContext = Span.current().getSpanContext();
        if (spanContext.isValid()) {
            return spanContext.getTraceId();
        }
        return "";
    }

    /**
     * Gets the current span ID from the active span.
     *
     * @return The span ID, or an empty string if no span is active
     */
    public static String getSpanId() {
        SpanContext spanContext = Span.current().getSpanContext();
        if (spanContext.isValid()) {
            return spanContext.getSpanId();
        }
        return "";
    }

    /**
     * Checks if there is a valid trace context.
     *
     * @return true if there is a valid trace context, false otherwise
     */
    public static boolean hasTraceContext() {
        return Span.current().getSpanContext().isValid();
    }
}
