package com.veefin.ual.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.support.TaskExecutorAdapter;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executors;

/**
 * Configuration class for virtual threads.
 * This class configures the application to use virtual threads for asynchronous operations.
 */
@Configuration
@EnableAsync
public class VirtualThreadConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(VirtualThreadConfig.class);
    
    /**
     * Creates a virtual thread executor for asynchronous tasks.
     * 
     * @return An AsyncTaskExecutor that uses virtual threads
     */
    @Bean
    public AsyncTaskExecutor applicationTaskExecutor() {
        logger.info("Configuring virtual thread executor");
        return new TaskExecutorAdapter(Executors.newVirtualThreadPerTaskExecutor());
    }
}
