package com.veefin.ual.repository;

import com.veefin.ual.config.TestConfig;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@ActiveProfiles("test")
@Import(TestConfig.class)
public class EventLogRepositoryTest {

    @Autowired
    private EventLogRepository eventLogRepository;

    @Autowired
    private UserActivitySessionRepository userActivitySessionRepository;

    private EventLog eventLog;
    private UserActivitySession userActivitySession;
    private String sessionId;
    private LocalDateTime startDate;
    private LocalDateTime endDate;

    @BeforeEach
    void setUp() {
        // Initialize test data
        sessionId = UUID.randomUUID().toString();
        startDate = LocalDateTime.now().minusDays(7);
        endDate = LocalDateTime.now().plusDays(1);

        userActivitySession = new UserActivitySession();
        userActivitySession.setId(sessionId);
        userActivitySession.setEmail("<EMAIL>");
        userActivitySession.setName("Test User");
        userActivitySession.setUserUuid("test-uuid");
        userActivitySession.setSystemName("TEST");
        userActivitySession.setUserAgent("Test Agent");
        userActivitySession.setClientIp("127.0.0.1");
        userActivitySession.setSource("WEB");
        userActivitySession.setCreatedAt(LocalDateTime.now());

        // Save the session
        userActivitySessionRepository.save(userActivitySession);

        eventLog = new EventLog();
        eventLog.setId(UUID.randomUUID().toString());
        eventLog.setUserActivitySessionId(sessionId);
        eventLog.setModule("TEST_MODULE");
        eventLog.setAction("TEST_ACTION");
        eventLog.setSystemName("TEST");
        eventLog.setCreatedAt(LocalDateTime.now());
        eventLog.setEventAt(LocalDateTime.now());

        // Save the event log
        eventLogRepository.save(eventLog);
    }

    @Test
    void testSearchBySessionIdAndFields() {
        // Create pageable
        Pageable pageable = PageRequest.of(0, 10);

        // Call the repository method
        Page<EventLog> result = eventLogRepository.searchBySessionIdAndFields(sessionId, "TEST", startDate, endDate, pageable);

        // Verify the result
        assertNotNull(result);
        assertTrue(result.hasContent());
        assertEquals(1, result.getTotalElements());

        EventLog log = result.getContent().get(0);
        assertEquals(sessionId, log.getUserActivitySessionId());
        assertEquals("TEST_MODULE", log.getModule());
        assertEquals("TEST_ACTION", log.getAction());
    }

    @Test
    void testSearchEvents() {
        // Create pageable
        Pageable pageable = PageRequest.of(0, 10);

        // Call the repository method
        Page<EventLog> result = eventLogRepository.searchEvents(sessionId, "TEST_MODULE", "TEST_ACTION", "TEST", startDate, endDate, pageable);

        // Verify the result
        assertNotNull(result);
        assertTrue(result.hasContent());
        assertEquals(1, result.getTotalElements());

        EventLog log = result.getContent().get(0);
        assertEquals(sessionId, log.getUserActivitySessionId());
        assertEquals("TEST_MODULE", log.getModule());
        assertEquals("TEST_ACTION", log.getAction());
    }

    @Test
    void testSearchEventsWithNoMatch() {
        // Create pageable
        Pageable pageable = PageRequest.of(0, 10);

        // Call the repository method with a module that won't match
        Page<EventLog> result = eventLogRepository.searchEvents(sessionId, "NONEXISTENT", null, null, startDate, endDate, pageable);

        // Verify the result
        assertNotNull(result);
        assertFalse(result.hasContent());
        assertEquals(0, result.getTotalElements());
    }
}
