package com.veefin.ual.actuator;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;

/**
 * Custom health indicator that provides application status information
 */
@Component
public class ApplicationHealthIndicator implements HealthIndicator {

    @Value("${spring.application.name:ual}")
    private String applicationName;

    @Value("${spring.profiles.active:none}")
    private String activeProfile;

    @Override
    public Health health() {
        try {
            // Get runtime information
            RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
            long uptime = runtimeMXBean.getUptime();

            // Get memory information
            MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
            long heapUsed = memoryMXBean.getHeapMemoryUsage().getUsed();
            long heapMax = memoryMXBean.getHeapMemoryUsage().getMax();
            double heapUsagePercent = (double) heapUsed / heapMax * 100;

            return Health.up()
                    .withDetail("application", applicationName)
                    .withDetail("profile", activeProfile)
                    .withDetail("uptime_ms", uptime)
                    .withDetail("heap_used_mb", heapUsed / (1024 * 1024))
                    .withDetail("heap_max_mb", heapMax / (1024 * 1024))
                    .withDetail("heap_usage_percent", String.format("%.2f", heapUsagePercent))
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("application", applicationName)
                    .withDetail("profile", activeProfile)
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", System.currentTimeMillis())
                    .build();
        }
    }
}
