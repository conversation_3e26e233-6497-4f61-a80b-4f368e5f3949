package com.veefin.ual.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Paginated result container for user activity events")
public class UserActivityEventPaginatedDTO {

	@JsonProperty("total_count")
	@Schema(description = "Total number of events matching the search criteria")
	private long totalCount;

	@JsonProperty("events")
	@Schema(description = "List of events for the current page")
	private List<UserActivityEventResponseDTO> events;

	public UserActivityEventPaginatedDTO(long totalCount, List<UserActivityEventResponseDTO> events) {
		this.totalCount = totalCount;
		this.events = events;
	}

	public long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}

	public List<UserActivityEventResponseDTO> getEvents() {
		return events;
	}

	public void setEvents(List<UserActivityEventResponseDTO> events) {
		this.events = events;
	}

}
