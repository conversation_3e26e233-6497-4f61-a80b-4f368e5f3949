package com.veefin.ual.specification;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;

import com.veefin.ual.dto.EventSearchRequestDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;

public class EventLogPaginationSpecification {

	private static String createLikePattern(String value) {
		return "%" + value.toLowerCase() + "%";
	}

	public static Specification<EventLog> fromDTO(EventSearchRequestDTO dto) {
		return (root, query, cb) -> {
			List<Predicate> andPredicates = new ArrayList<>();

			// Direct filters
			if (dto.getModule() != null) {
				andPredicates.add(cb.like(cb.lower(root.get("module")), createLikePattern(dto.getModule())));
			}
			if (dto.getAction() != null) {
				andPredicates.add(cb.like(cb.lower(root.get("action")), createLikePattern(dto.getAction())));
			}
			if (dto.getSystemName() != null) {
				andPredicates.add(cb.like(cb.lower(root.get("systemName")), createLikePattern(dto.getSystemName())));
			}
			if (dto.getUserActivitySessionId() != null) {
				andPredicates.add(cb.equal(root.get("userActivitySessionId"), dto.getUserActivitySessionId()));
			}
			if (dto.getClientIp() != null)
				andPredicates.add(cb.like(cb.lower(root.get("clientIp")), "%" + dto.getClientIp().toLowerCase() + "%"));
			if (dto.getUserAgent() != null)
				andPredicates
						.add(cb.like(cb.lower(root.get("userAgent")), "%" + dto.getUserAgent().toLowerCase() + "%"));

			// Join to session for userName
			Join<EventLog, UserActivitySession> sessionJoin = null;
			if (dto.getUserName() != null || (dto.getSearch() != null && !dto.getSearch().isBlank())) {
				sessionJoin = root.join("userActivitySession", JoinType.LEFT);
				if (dto.getUserName() != null)
					andPredicates.add(
							cb.like(cb.lower(sessionJoin.get("name")), "%" + dto.getUserName().toLowerCase() + "%"));
			}
			if (dto.getUserEmail() != null || (dto.getSearch() != null && !dto.getSearch().isBlank())) {
				sessionJoin = root.join("userActivitySession", JoinType.LEFT);
				if (dto.getUserEmail() != null)
					andPredicates.add(
							cb.like(cb.lower(sessionJoin.get("email")), "%" + dto.getUserEmail().toLowerCase() + "%"));
			}

			// OR-based fulltext search
			if (dto.getSearch() != null && !dto.getSearch().isBlank()) {
				String keyword = "%" + dto.getSearch().toLowerCase() + "%";
				if (sessionJoin == null) {
					sessionJoin = root.join("userActivitySession", JoinType.LEFT);
				}
				Predicate orPredicate = cb.or(cb.like(cb.lower(root.get("module")), keyword),
						cb.like(cb.lower(root.get("action")), keyword), cb.like(cb.lower(root.get("message")), keyword),
						cb.like(cb.lower(root.get("userAgent")), keyword),
						cb.like(cb.lower(root.get("clientIp")), keyword),
						cb.like(cb.lower(sessionJoin.get("name")), keyword),
						cb.like(cb.lower(sessionJoin.get("email")), keyword));
				andPredicates.add(orPredicate);
			}

			if (dto.getFromDate() != null)
				andPredicates.add(cb.greaterThanOrEqualTo(root.get("eventAt"), dto.getFromDate().atStartOfDay()));

			if (dto.getToDate() != null)
				andPredicates.add(cb.lessThanOrEqualTo(root.get("eventAt"), dto.getToDate().atTime(23, 59, 59)));

			return cb.and(andPredicates.toArray(new Predicate[0]));
		};
	}
}
