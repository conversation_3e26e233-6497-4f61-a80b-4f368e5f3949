package com.veefin.ual.service.impl;

import com.veefin.ual.service.SentryService;
import io.sentry.Scope;
import io.sentry.Sentry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;

/**
 * Implementation of SentryService that provides robust error handling
 * when Sentry is not available or not properly configured.
 */
@Service
public class SentryServiceImpl implements SentryService {

    private static final Logger logger = LoggerFactory.getLogger(SentryServiceImpl.class);

    @Value("${sentry.enabled:true}")
    private boolean sentryEnabled;

    @Value("${sentry.dsn:}")
    private String sentryDsn;

    private volatile Boolean sentryAvailable;

    /**
     * Lazy initialization to check if Sentry is available.
     * This method is thread-safe and caches the result.
     */
    private boolean isSentryAvailable() {
        if (sentryAvailable == null) {
            synchronized (this) {
                if (sentryAvailable == null) {
                    sentryAvailable = checkSentryAvailability();
                }
            }
        }
        return sentryAvailable;
    }

    /**
     * Checks if Sentry is properly configured and available.
     */
    private boolean checkSentryAvailability() {
        try {
            // Check if Sentry is enabled via configuration
            if (!sentryEnabled) {
                logger.info("Sentry is disabled via configuration");
                return false;
            }

            // Check if DSN is configured
            if (sentryDsn == null || sentryDsn.trim().isEmpty()) {
                logger.warn("Sentry DSN is not configured, Sentry will be disabled");
                return false;
            }

            // Check if Sentry is initialized
            if (!Sentry.isEnabled()) {
                logger.warn("Sentry is not enabled or not properly initialized");
                return false;
            }

            logger.info("Sentry is available and properly configured");
            return true;
        } catch (Exception e) {
            logger.error("Error checking Sentry availability: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void captureException(Throwable throwable) {
        if (isSentryAvailable()) {
            try {
                Sentry.captureException(throwable);
                logger.debug("Exception captured by Sentry: {}", throwable.getMessage());
            } catch (Exception e) {
                logger.error("Failed to capture exception with Sentry, falling back to logging: {}", 
                    e.getMessage(), throwable);
            }
        } else {
            logger.error("Sentry not available, logging exception instead: {}", 
                throwable.getMessage(), throwable);
        }
    }

    @Override
    public void captureMessage(String message) {
        if (isSentryAvailable()) {
            try {
                Sentry.captureMessage(message);
                logger.debug("Message captured by Sentry: {}", message);
            } catch (Exception e) {
                logger.error("Failed to capture message with Sentry, falling back to logging: {}", 
                    e.getMessage());
                logger.warn("Original message: {}", message);
            }
        } else {
            logger.warn("Sentry not available, logging message instead: {}", message);
        }
    }

    @Override
    public void captureMessage(String message, Map<String, String> extras) {
        if (isSentryAvailable()) {
            try {
                Sentry.withScope(scope -> {
                    if (extras != null) {
                        extras.forEach(scope::setExtra);
                    }
                    Sentry.captureMessage(message);
                });
                logger.debug("Message with extras captured by Sentry: {}", message);
            } catch (Exception e) {
                logger.error("Failed to capture message with extras using Sentry, falling back to logging: {}", 
                    e.getMessage());
                logger.warn("Original message: {}, extras: {}", message, extras);
            }
        } else {
            logger.warn("Sentry not available, logging message with extras instead: {}, extras: {}", 
                message, extras);
        }
    }

    @Override
    public void withScope(Consumer<SentryScope> scopeCallback) {
        if (isSentryAvailable()) {
            try {
                Sentry.withScope(scope -> {
                    SentryScope wrappedScope = new SentryScopeWrapper((Scope) scope);
                    scopeCallback.accept(wrappedScope);
                });
            } catch (Exception e) {
                logger.error("Failed to execute Sentry scope operation, falling back to logging: {}", 
                    e.getMessage(), e);
                // Execute callback with a fallback scope that just logs
                FallbackSentryScope fallbackScope = new FallbackSentryScope();
                scopeCallback.accept(fallbackScope);
            }
        } else {
            // Execute callback with a fallback scope that just logs
            FallbackSentryScope fallbackScope = new FallbackSentryScope();
            scopeCallback.accept(fallbackScope);
        }
    }

    @Override
    public boolean isAvailable() {
        return isSentryAvailable();
    }

    /**
     * Wrapper for actual Sentry scope operations.
     */
    private static class SentryScopeWrapper implements SentryScope {
        private final io.sentry.Scope sentryScope;

        public SentryScopeWrapper(io.sentry.Scope sentryScope) {
            this.sentryScope = sentryScope;
        }

        @Override
        public void setExtra(String key, String value) {
            sentryScope.setExtra(key, value);
        }

        @Override
        public void captureMessage(String message) {
            Sentry.captureMessage(message);
        }
    }

    /**
     * Fallback scope implementation that logs instead of using Sentry.
     */
    private static class FallbackSentryScope implements SentryScope {
        private static final Logger fallbackLogger = LoggerFactory.getLogger(FallbackSentryScope.class);

        @Override
        public void setExtra(String key, String value) {
            fallbackLogger.debug("Sentry scope extra (fallback): {}={}", key, value);
        }

        @Override
        public void captureMessage(String message) {
            fallbackLogger.warn("Sentry message (fallback): {}", message);
        }
    }
}
