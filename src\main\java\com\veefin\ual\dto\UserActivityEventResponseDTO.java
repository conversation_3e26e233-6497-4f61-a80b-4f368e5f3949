package com.veefin.ual.dto;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Response DTO for user activity events with enhanced user information")
public class UserActivityEventResponseDTO {

	@JsonProperty("id")
	private String id;

	@JsonProperty("module")
	private String module;

	@JsonProperty("action")
	private String action;

	@JsonProperty("user_activity_sessionId")
	private String userActivitySessionId;

	@JsonProperty("system_name")
	private String systemName;

	@JsonProperty("properties")
	private String properties;

	@JsonProperty("client_ip")
	private String clientIp;

	@JsonProperty("user_agent")
	private String userAgent;

	@JsonProperty("event_at")
	private String eventAt;

	@JsonProperty("message")
	private String message;

	@JsonProperty("user_name")
	@Schema(description = "User name from the associated session")
	private String userName;

	@JsonProperty("user_email")
	@Schema(description = "User email from the associated session")
	private String userEmail;

	@JsonProperty("user_uuid")
	@Schema(description = "User UUID from the associated session")
	private String userUuid;

	// Define a formatter for the date-time format
	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");

	public UserActivityEventResponseDTO(EventLog eventLog) {
		this.id = eventLog.getId();
		this.module = eventLog.getModule();
		this.action = eventLog.getAction();
		this.userActivitySessionId = eventLog.getUserActivitySessionId();
		this.systemName = eventLog.getSystemName();
		this.properties = eventLog.getProperties();
		this.clientIp = eventLog.getClientIp();
		this.userAgent = eventLog.getUserAgent();
		this.eventAt = eventLog.getEventAt() != null ? eventLog.getEventAt().format(DATE_TIME_FORMATTER) : null;
		this.message = eventLog.getMessage();
	}

	/**
	 * Constructor that includes user session information
	 */
	public UserActivityEventResponseDTO(EventLog eventLog, UserActivitySession session) {
		this(eventLog); // Call the basic constructor first

		// Add session information if available
		if (session != null) {
			this.userName = session.getName();
			this.userEmail = session.getEmail();
			this.userUuid = session.getUserUuid();
		}
	}

	/**
	 * Constructor that uses the JPA relationship to get session information
	 */
	public UserActivityEventResponseDTO(EventLog eventLog, boolean includeSessionInfo) {
		this(eventLog); // Call the basic constructor first

		// Add session information if requested and available
		if (includeSessionInfo && eventLog.getUserActivitySession() != null) {
			UserActivitySession session = eventLog.getUserActivitySession();
			this.userName = session.getName();
			this.userEmail = session.getEmail();
			this.userUuid = session.getUserUuid();
		}
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getUserActivitySessionId() {
		return userActivitySessionId;
	}

	public void setUserActivitySessionId(String userActivitySessionId) {
		this.userActivitySessionId = userActivitySessionId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getProperties() {
		return properties;
	}

	public void setProperties(String properties) {
		this.properties = properties;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getEventAt() {
		return eventAt;
	}

	public void setEventAt(String eventAt) {
		this.eventAt = eventAt;
	}

	/**
	 * Sets the eventAt field by converting a LocalDateTime to String in the format
	 * dd-MM-yyyy HH:mm:ss
	 */
	public void setEventAt(LocalDateTime eventAt) {
		this.eventAt = eventAt != null ? eventAt.format(DATE_TIME_FORMATTER) : null;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getUserUuid() {
		return userUuid;
	}

	public void setUserUuid(String userUuid) {
		this.userUuid = userUuid;
	}

}
