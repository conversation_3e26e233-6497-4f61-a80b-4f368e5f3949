package com.veefin.ual.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.veefin.ual.entity.UserActivitySession;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class UserActivitySessionResponseDTO {

	@JsonProperty("id")
	private String id;

	@JsonProperty("name")
	private String name;

	@JsonProperty("email")
	private String email;

	@JsonProperty("user_uuid")
	private String userUuid;

	@JsonProperty("system_name")
	private String systemName;

	@JsonProperty("user_agent")
	private String userAgent;

	@JsonProperty("client_ip")
	private String clientIp;

	@JsonProperty("source")
	private String source;

	@JsonProperty("created_at")
	private String createdAt;

	// Define a formatter for the date-time format
	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");

	public UserActivitySessionResponseDTO() {
	}

	public UserActivitySessionResponseDTO(UserActivitySession session) {
		this.id = session.getId();
		this.name = session.getName();
		this.email = session.getEmail();
		this.userUuid = session.getUserUuid();
		this.systemName = session.getSystemName();
		this.userAgent = session.getUserAgent();
		this.clientIp = session.getClientIp();
		this.source = session.getSource();
		this.createdAt = session.getCreatedAt() != null ? session.getCreatedAt().format(DATE_TIME_FORMATTER) : null;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUserUuid() {
		return userUuid;
	}

	public void setUserUuid(String userUuid) {
		this.userUuid = userUuid;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(LocalDateTime createdAt) {
		this.createdAt = createdAt != null ? createdAt.format(DATE_TIME_FORMATTER) : null;
	}

	public UserActivitySessionResponseDTO(String name, String email, String userUuid, String systemName,
			String userAgent, String clientIp, String source) {
		this.name = name;
		this.email = email;
		this.userUuid = userUuid;
		this.systemName = systemName;
		this.userAgent = userAgent;
		this.clientIp = clientIp;
		this.source = source;
	}

}
