package com.veefin.ual.interceptor;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import io.opentelemetry.context.propagation.TextMapGetter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Collections;

/**
 * Interceptor for handling OpenTelemetry trace context in HTTP requests.
 * This interceptor creates spans for incoming HTTP requests and propagates trace context.
 */
@Component
public class OpenTelemetryInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(OpenTelemetryInterceptor.class);

    private final OpenTelemetry openTelemetry;
    private final Tracer tracer;

    @Autowired
    public OpenTelemetryInterceptor(OpenTelemetry openTelemetry) {
        this.openTelemetry = openTelemetry;
        this.tracer = openTelemetry.getTracer("user-activity-logger");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // Extract context from the request headers
        Context extractedContext = openTelemetry.getPropagators().getTextMapPropagator()
                .extract(Context.current(), request, new HttpServletRequestGetter());

        // Create a span for this request
        Span span = tracer.spanBuilder(request.getMethod() + " " + request.getRequestURI())
                .setParent(extractedContext)
                .setSpanKind(SpanKind.SERVER)
                .startSpan();

        // Add attributes to the span
        span.setAttribute("http.method", request.getMethod());
        span.setAttribute("http.url", request.getRequestURL().toString());
        span.setAttribute("http.path", request.getRequestURI());
        span.setAttribute("http.host", request.getServerName());
        span.setAttribute("http.scheme", request.getScheme());
        span.setAttribute("http.user_agent", request.getHeader("User-Agent"));

        // Make the span the current span
        Scope scope = span.makeCurrent();

        // Store the span and scope in the request attributes for later use
        request.setAttribute("span", span);
        request.setAttribute("scope", scope);

        // Add trace and span IDs to MDC for logging
        org.slf4j.MDC.put("trace_id", span.getSpanContext().getTraceId());
        org.slf4j.MDC.put("span_id", span.getSpanContext().getSpanId());

        logger.debug("Started span for request: {} with trace ID: {}",
                request.getRequestURI(), span.getSpanContext().getTraceId());

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // Retrieve the span and scope from the request attributes
        Span span = (Span) request.getAttribute("span");
        Scope scope = (Scope) request.getAttribute("scope");

        try {
            if (span != null) {
                // Add response status code to span
                span.setAttribute("http.status_code", response.getStatus());

                // If there was an exception, record it in the span
                if (ex != null) {
                    span.recordException(ex);
                    span.setStatus(io.opentelemetry.api.trace.StatusCode.ERROR, ex.getMessage());
                    logger.error("Exception in request processing", ex);
                }

                // Close the scope if it exists
                if (scope != null) {
                    scope.close();
                }

                // End the span
                span.end();
                logger.debug("Ended span for request: {} with trace ID: {}",
                        request.getRequestURI(), span.getSpanContext().getTraceId());
            }
        } finally {
            // Clear MDC to prevent leaks
            org.slf4j.MDC.remove("trace_id");
            org.slf4j.MDC.remove("span_id");
        }
    }

    /**
     * TextMapGetter implementation for extracting context from HTTP headers.
     */
    private static class HttpServletRequestGetter implements TextMapGetter<HttpServletRequest> {
        @Override
        public Iterable<String> keys(HttpServletRequest carrier) {
            return Collections.list(carrier.getHeaderNames());
        }

        @Override
        public String get(HttpServletRequest carrier, String key) {
            return carrier.getHeader(key);
        }
    }
}
