package com.veefin.ual.config;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.security.oauth2.server.resource.authentication.JwtIssuerAuthenticationManagerResolver;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import com.veefin.oauth.resource.provider.config.DefaultJwtAuthenticationProviderFactory;
import com.veefin.workflow.wrapper.security.config.CustomJwtAuthenticationProviderFactory;

@Configuration
@EnableMethodSecurity
@EnableWebSecurity
public class SecurityConfig {
	@Value("${oauth.server.issuer}")
	private String OAuthIssuer;
	private final Map<String, JwtAuthenticationProvider> providerCache = new ConcurrentHashMap<>();
	private final DefaultJwtAuthenticationProviderFactory defaultJwtAuthenticationProviderFactory;
	private final CustomJwtAuthenticationProviderFactory customJwtAuthenticationProviderFactory;

	public SecurityConfig(DefaultJwtAuthenticationProviderFactory defaultJwtAuthenticationProviderFactory,
			CustomJwtAuthenticationProviderFactory customJwtAuthenticationProviderFactory) {
		this.defaultJwtAuthenticationProviderFactory = defaultJwtAuthenticationProviderFactory;
		this.customJwtAuthenticationProviderFactory = customJwtAuthenticationProviderFactory;
	}

	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity httpSecurity) throws Exception {
		JwtIssuerAuthenticationManagerResolver authenticationManagerResolver = new JwtIssuerAuthenticationManagerResolver(
				this::getAuthenticationManager);
		return httpSecurity.csrf(AbstractHttpConfigurer::disable) // enable
				.cors(httpSecurityCorsConfigurer -> httpSecurityCorsConfigurer
						.configurationSource(corsConfigurationSource()))
				.authorizeHttpRequests(auth -> auth
						.requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html",
								"/v3/generate-sample/**", "/actuator/**","/**")
						.permitAll()
						// .requestMatchers("/master/tables/**").hasRole("ADMIN")
						.anyRequest().authenticated())
				//.oauth2ResourceServer(oauth2 -> oauth2.authenticationManagerResolver(authenticationManagerResolver))
				.build();
	}

	@Bean
	public CorsConfigurationSource corsConfigurationSource() {
		CorsConfiguration configuration = new CorsConfiguration();
		configuration.setAllowedOrigins(List.of("*")); // Allow all origins
		configuration.setAllowedMethods(List.of("*")); // Allow all HTTP methods
		configuration.setAllowedHeaders(List.of("*")); // Allow all headers
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		source.registerCorsConfiguration("/**", configuration);
		return source;
	}

	public AuthenticationManager getAuthenticationManager(String issuer) {
		JwtAuthenticationProvider provider = providerCache.computeIfAbsent(issuer, this::createProvider);
		return provider::authenticate;
	}

	private JwtAuthenticationProvider createProvider(String issuer) {
		if ("veefin".equals(issuer)) {
			return customJwtAuthenticationProviderFactory.createProvider(issuer);
		} else if (OAuthIssuer.equals(issuer)) {
			// return null;
			return defaultJwtAuthenticationProviderFactory.createProvider(issuer);
		} else {
			// throw new NotFoundException("Issuer not found: " + issuer);
			throw new IllegalArgumentException("Issuer not found: " + issuer);
		}
	}

}
