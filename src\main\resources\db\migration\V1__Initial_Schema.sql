-- Initial schema creation for User Activity Logger

-- User Activity Sessions table
CREATE TABLE IF NOT EXISTS user_activity_sessions (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    name VARCHAR(255) ,
    email VARCHAR(255) ,
    user_uuid VARCHAR(255),
    system_name VARCHAR(255) NOT NULL,
    user_agent VARCHAR(500) ,
    client_ip VARCHAR(50) ,
    source VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User Activity Events table
CREATE TABLE IF NOT EXISTS user_activity_events (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    module VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    user_activity_session_id VARCHAR(36) NOT NULL,
    system_name VARCHAR(100) NOT NULL,
    properties JSON,
    client_ip VARCHAR(50),
    user_agent VARCHAR(1000),
    event_at TIMESTAMP NOT NULL,
    message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_session FOREIGN KEY (user_activity_session_id) REFERENCES user_activity_sessions(id)
);

-- Indexes for better query performance
CREATE INDEX idx_events_session_id ON user_activity_events(user_activity_session_id);
CREATE INDEX idx_events_module ON user_activity_events(module);
CREATE INDEX idx_events_action ON user_activity_events(action);
CREATE INDEX idx_events_system_name ON user_activity_events(system_name);
CREATE INDEX idx_events_event_at ON user_activity_events(event_at);
CREATE INDEX idx_sessions_name ON user_activity_sessions(name);
CREATE INDEX idx_sessions_email ON user_activity_sessions(email);
CREATE INDEX idx_sessions_user_uuid ON user_activity_sessions(user_uuid);
CREATE INDEX idx_sessions_system_name ON user_activity_sessions(system_name);
