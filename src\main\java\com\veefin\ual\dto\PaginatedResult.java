package com.veefin.ual.dto;

import java.util.List;

public class PaginatedResult<T> {
    private List<T> results;
    private long totalCount;

    public PaginatedResult() {
    }

    public PaginatedResult(List<T> results, long totalCount) {
        this.results = results;
        this.totalCount = totalCount;
    }
    public List<T> getResults() {
        return results;
    }
    public long getTotalCount() {
        return totalCount;
    }

    public void setResults(List<T> results) {
        this.results = results;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }
}