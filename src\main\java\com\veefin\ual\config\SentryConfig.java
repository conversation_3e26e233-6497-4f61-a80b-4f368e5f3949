package com.veefin.ual.config;

import com.veefin.ual.service.SentryService;
import io.opentelemetry.api.trace.Span;
import io.sentry.EventProcessor;
import io.sentry.Hint;
import io.sentry.SentryEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * Configuration class for Sentry integration.
 * This customizes the Sentry configuration provided by Spring Boot.
 */
@Configuration
public class SentryConfig {

    private static final Logger logger = LoggerFactory.getLogger(SentryConfig.class);

    private final SentryService sentryService;

    @Value("${spring.application.name}")
    private String serviceName;

    public SentryConfig(SentryService sentryService) {
        this.sentryService = sentryService;
    }

    /**
     * Customizes Sentry options after the application has started.
     * This ensures we're using the SentryOptions bean created by Spring Boot.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void configureSentry() {
        if (!sentryService.isAvailable()) {
            logger.warn("Sentry is not available, skipping OpenTelemetry integration configuration");
            return;
        }

        logger.info("Configuring Sentry integration with OpenTelemetry");

        try {
            // Add OpenTelemetry integration to Sentry
            io.sentry.Sentry.configureScope(scope -> {
                scope.addEventProcessor(new OpenTelemetryEventProcessor());
            });

            logger.info("Sentry integration with OpenTelemetry configured successfully");
        } catch (Exception e) {
            logger.error("Failed to configure Sentry integration with OpenTelemetry: {}", e.getMessage(), e);
        }
    }

    /**
     * Custom event processor that adds OpenTelemetry trace and span IDs to Sentry events.
     */
    private class OpenTelemetryEventProcessor implements EventProcessor {
        @Override
        public SentryEvent process(SentryEvent event, Hint hint) {
            // Add trace ID from OpenTelemetry if available
            String traceId = Span.current().getSpanContext().getTraceId();
            if (traceId != null && !traceId.isEmpty() && Span.current().getSpanContext().isValid()) {
                event.setTag("otel.trace_id", traceId);
            }

            // Add span ID from OpenTelemetry if available
            String spanId = Span.current().getSpanContext().getSpanId();
            if (spanId != null && !spanId.isEmpty() && Span.current().getSpanContext().isValid()) {
                event.setTag("otel.span_id", spanId);
            }

            // Add service name
            event.setTag("service.name", serviceName);

            // Add environment
            event.setTag("environment", System.getProperty("spring.profiles.active", "local"));

            return event;
        }
    }
}
