package com.veefin.ual.exceptionhandler;

import com.veefin.ual.utilities.ErrorCode;

public class VeefinException extends RuntimeException {

	private static final long serialVersionUID = 7953163824003364653L;
	private final ErrorCode errorCode;
	private final Object[] messageArgs;

	public VeefinException(ErrorCode errorCode) {
		this.errorCode = errorCode;
		this.messageArgs = new Object[] {};
	}

	public VeefinException(ErrorCode errorCode, Object... messageArgs) {
		this.errorCode = errorCode;
		this.messageArgs = messageArgs;
	}

	public VeefinException(ErrorCode errorCode, Throwable cause) {
		super(cause);
		this.errorCode = errorCode;
		this.messageArgs = new Object[] {};
	}

	public ErrorCode getErrorCode() {
		return errorCode;
	}

	public Object[] getMessageArgs() {
		return messageArgs;
	}
}
