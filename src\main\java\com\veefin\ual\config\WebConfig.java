package com.veefin.ual.config;

import com.veefin.ual.interceptor.OpenTelemetryInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web configuration class for registering interceptors.
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final OpenTelemetryInterceptor openTelemetryInterceptor;

    @Autowired
    public WebConfig(OpenTelemetryInterceptor openTelemetryInterceptor) {
        this.openTelemetryInterceptor = openTelemetryInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Register the OpenTelemetry interceptor to handle all requests
        registry.addInterceptor(openTelemetryInterceptor);
    }
}
