package com.veefin.ual.config;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

/**
 * Configuration class to set the default timezone for the application.
 * This ensures all timestamps in logs and other outputs use the specified timezone.
 */
@Configuration
public class TimeZoneConfig {

    private static final Logger logger = LoggerFactory.getLogger(TimeZoneConfig.class);
    private static final String IST_TIMEZONE = "Asia/Kolkata";

    /**
     * Sets the default timezone to IST (Indian Standard Time) when the application starts.
     */
    @PostConstruct
    public void init() {
        TimeZone.setDefault(TimeZone.getTimeZone(IST_TIMEZONE));
        logger.info("Application timezone set to: {}", TimeZone.getDefault().getID());
    }
}
