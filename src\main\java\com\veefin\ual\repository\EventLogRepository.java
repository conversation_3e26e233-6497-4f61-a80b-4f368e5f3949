package com.veefin.ual.repository;

import java.time.LocalDateTime;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.veefin.ual.entity.EventLog;

@Repository
public interface EventLogRepository extends JpaRepository<EventLog, String>, JpaSpecificationExecutor<EventLog> {
    
	@Query(value = Queries.LISTING_QUERY + Queries.LISTING_WHERE_QUERY,
            countQuery = Queries.LISTING_COUNT_QUERY)
    Page<EventLog> searchBySessionIdAndFields(@Param("sessionId") String sessionId, @Param("search") String search, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate,
            Pageable pageable);
    
    @Query(value = Queries.LISTING_QUERY + Queries.EVENT_LISTING,
            countQuery = Queries.LISTING_COUNT_QUERY)
    Page<EventLog>searchEvents(@Param("userActivitySessionId") String userActivitySessionId,@Param("module") String module ,@Param("action") String action,@Param("systemName") String systemName,@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate,Pageable pageable);
    class Queries {
        public static final String EVENT_LISTING=" WHERE " +
                "(:userActivitySessionId IS NULL OR e.userActivitySessionId = :userActivitySessionId)"+
        "AND (:module IS NULL OR :module = '' OR LOWER(e.module) LIKE LOWER(CONCAT('%', :module, '%')))"+
        "AND (:action IS NULL OR :action = '' OR LOWER(e.action) LIKE LOWER(CONCAT('%', :action, '%')))"+
        "AND (:systemName IS NULL OR :systemName = '' OR LOWER(e.systemName) LIKE LOWER(CONCAT('%', :systemName, '%')))"+
        "AND ((:startDate IS NULL OR :endDate IS NULL) OR e.eventAt BETWEEN :startDate AND :endDate)";

        public static final String LISTING_WHERE_QUERY =
                " WHERE " +
                        " (:sessionId IS NULL OR e.userActivitySessionId = :sessionId) " +
                        " AND (:search IS NULL OR " +
                        " LOWER(e.userActivitySessionId) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
                        " LOWER(e.module) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
                        " LOWER(e.action) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
                        " LOWER(e.systemName) LIKE LOWER(CONCAT('%', :search, '%'))) " +
                        " AND ((:startDate IS NULL OR :endDate IS NULL) OR e.createdAt BETWEEN :startDate AND :endDate)";

        public static final String LISTING_QUERY =
                "SELECT e FROM EventLog e";

        public static final String LISTING_COUNT_QUERY =
                "SELECT COUNT(e) FROM EventLog e " + EVENT_LISTING;
    }


}
