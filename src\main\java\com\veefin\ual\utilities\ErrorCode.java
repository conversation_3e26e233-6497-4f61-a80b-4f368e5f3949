package com.veefin.ual.utilities;


public enum ErrorCode {
    // Generic errors (1000-1999)
    GENERIC_ERROR("ERR-1000", "error.generic"),
    VALIDATION_ERROR("CMS-PS-40022", "error.validation"),
    RESOURCE_NOT_FOUND("ERR-404", "error.resource.notfound"),
    BAD_REQUEST("ERR-400", "error.resource.notfound"),

    // Security errors (3000-3999)
    UNAUTHORIZED("ERR-401", "error.security.unauthorized"),
    FORBIDDEN("ERR-403", "error.security.forbidden");

    private final String code;
    private final String message;

    ErrorCode(String code, String message) {
        this.code=code;
        this.message=message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
