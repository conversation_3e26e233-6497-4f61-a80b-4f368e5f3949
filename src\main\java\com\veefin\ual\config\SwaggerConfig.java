package com.veefin.ual.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class SwaggerConfig {

    @Value("${spring.application.name:ual}")
    private String applicationName;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("User Activity Logger API")
                        .description("A comprehensive service for tracking and querying user activity events across multiple systems.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Veefin Support")
                                .email("<EMAIL>")
                                .url("https://veefin.com"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://veefin.com/terms")))
                .servers(Arrays.asList(
                        new Server().url("/ual").description("UAL Context Path (Use this)"),
                        new Server().url("/").description("Default Server (Not recommended)")))
                .tags(Arrays.asList(
                        new Tag().name("User Activity Controller").description("APIs for user activity sessions and events"),
                        new Tag().name("Event Search").description("APIs for searching user activity events"),
                        new Tag().name("Session Search").description("APIs for searching user sessions")))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("JWT token authentication. Enter the token without the 'Bearer ' prefix.")
                        )
                );
    }
}
