package com.veefin.ual.entity;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

@Entity
@Table(name = "user_activity_sessions")
public class UserActivitySession extends BaseEntity {

	@Column(name = "name", nullable = true, length = 255)
	private String name;

	@Column(name = "email", nullable = true, length = 255)
	private String email;

	@Column(name = "user_uuid", nullable = true, length = 255)
	private String userUuid;

	@Column(name = "system_name", nullable = false, length = 255)
	private String systemName;

	@Column(name = "user_agent", nullable = true, length = 500)
	private String userAgent;

	@Column(name = "client_ip", nullable = true, length = 50)
	private String clientIp;

	@Column(name = "source")
	private String source;

	@OneToMany(mappedBy = "userActivitySession", fetch = FetchType.LAZY)
	private List<EventLog> eventLogs = new ArrayList<>();

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUserUuid() {
		return userUuid;
	}

	public void setUserUuid(String userUuid) {
		this.userUuid = userUuid;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public List<EventLog> getEventLogs() {
		return eventLogs;
	}

	public void setEventLogs(List<EventLog> eventLogs) {
		this.eventLogs = eventLogs;

		// Maintain bidirectional relationship
		if (eventLogs != null) {
			for (EventLog eventLog : eventLogs) {
				eventLog.setUserActivitySession(this);
			}
		}
	}

	/**
	 * Helper method to add an event log and maintain the bidirectional relationship
	 */
	public void addEventLog(EventLog eventLog) {
		if (eventLog != null) {
			this.eventLogs.add(eventLog);
			eventLog.setUserActivitySession(this);
		}
	}

	/**
	 * Helper method to remove an event log and maintain the bidirectional
	 * relationship
	 */
	public void removeEventLog(EventLog eventLog) {
		if (eventLog != null) {
			this.eventLogs.remove(eventLog);
		}
	}

	public UserActivitySession() {
	}

	public UserActivitySession(String name, String email, String userUuid, String systemName, String userAgent,
			String clientIp, String source) {
		this.name = name;
		this.email = email;
		this.userUuid = userUuid;
		this.systemName = systemName;
		this.userAgent = userAgent;
		this.clientIp = clientIp;
		this.source = source;
	}

}
