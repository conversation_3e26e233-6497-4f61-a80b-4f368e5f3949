package com.veefin.ual.service;

import com.veefin.ual.dto.EventSearchRequestDTO;
import com.veefin.ual.dto.ResponseDTO;

/**
 * Service for searching and retrieving event logs with various filtering options.
 */
public interface EventSearchService {
    
    /**
     * Search for events based on criteria specified in the request DTO.
     * 
     * @param requestDto Contains search parameters, filters, and pagination information
     * @return ResponseDTO containing the paginated event results
     */
    ResponseDTO<?> searchEvents(EventSearchRequestDTO requestDto);
}
