# User Activity Logger (UAL)

A robust service for tracking and analyzing user activity across applications.

## Overview

The User Activity Logger (UAL) is a microservice designed to capture, store, and retrieve user activity data across different applications. It provides a centralized logging system for user sessions and events, enabling comprehensive user behavior analysis and audit trails.

## Features

- **Session Tracking**: Create and manage user activity sessions
- **Event Logging**: Record detailed user actions and events
- **Asynchronous Processing**: Handle high-volume event logging with virtual threads
- **Flexible Storage**: Store events in database or publish to RabbitMQ
- **Advanced Search**: Filter and search user sessions and events
- **Circuit Breaker Pattern**: Fallback to database when RabbitMQ is unavailable
- **JWT Authentication**: Secure API access with JWT tokens

## Technology Stack

- **Java 21**: Utilizing the latest Java features including virtual threads
- **Spring Boot 3.3.2**: Modern Spring Boot framework
- **Spring Data JPA**: Database access and ORM
- **MySQL**: Primary database for production
- **H2**: In-memory database for testing
- **RabbitMQ**: Message broker for event publishing
- **Redis**: Session storage and JWT token validation
- **Flyway**: Database migration management
- **Swagger/OpenAPI**: API documentation
- **JaCoCo**: Code coverage analysis
- **Sentry**: Error tracking and monitoring

## Getting Started

### Prerequisites

- Java 21 or higher
- MySQL 8.0 or higher
- RabbitMQ 3.8 or higher
- Redis 6.0 or higher

### Local Setup

1. **Clone the repository**

```bash
git clone <repository-url>
cd user-activity-logger
```

2. **Configure the application**

Edit `src/main/resources/application-local.properties` to match your local environment:

```properties
# Database configuration
spring.datasource.url=***************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# RabbitMQ configuration
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest

# Redis configuration
spring.redis.host=localhost
spring.redis.port=6379
```

3. **Build the application**

```bash
mvn clean install
```

4. **Run the application**

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

The application will be available at `http://localhost:7082/ual`

## API Documentation

Once the application is running, you can access the Swagger UI at:

```
http://localhost:7082/ual/swagger-ui/index.html
```

### Key Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/v1/identify` | POST | Register a new user session |
| `/v1/events` | POST | Log user events (asynchronous) |
| `/v1/events/search` | POST | Search for user events with advanced filtering |
| `/v1/user-activity-sessions/search` | POST | Search for user sessions |

## Configuration

The application uses a hierarchical configuration approach:

1. **Base Configuration**: `application.properties`
2. **Environment-Specific**: `application-{env}.properties`
3. **Environment Variables**: Override properties at runtime

### Key Configuration Properties

| Property | Description | Default |
|----------|-------------|---------|
| `server.port` | Application port | 7082 |
| `rabbitmq.queue.name` | RabbitMQ queue name | event-message-queue |
| `rabbitmq.exchange.name` | RabbitMQ exchange name | event-message-exchange |
| `rabbitmq.routing.key` | RabbitMQ routing key | event_message |
| `add.event.db` | Store events in database | false |
| `spring.flyway.enabled` | Enable Flyway migrations | true |

## Database Schema

The application uses two main tables:

1. **user_activity_sessions**: Stores user session information
2. **user_activity_events**: Stores detailed user events

Flyway migrations in `src/main/resources/db/migration` manage the database schema.

## Testing

### Running Tests

```bash
mvn test
```

### Code Coverage

```bash
mvn verify
```

View the coverage report at `target/site/jacoco/index.html`

## Deployment

### Docker

Build the Docker image:

```bash
docker build -t user-activity-logger:latest .
```

Run the container:

```bash
docker run -p 7082:7082 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e RABBITMQ_HOST=rabbitmq-host \
  -e RABBITMQ_PORT=5672 \
  -e RABBITMQ_USERNAME=username \
  -e RABBITMQ_PASSWORD=password \
  -e SECURITY_REDIS_HOST=redis-host \
  -e SECURITY_REDIS_PORT=6379 \
  user-activity-logger:latest
```

### Environment Variables

| Variable | Description |
|----------|-------------|
| `SPRING_PROFILES_ACTIVE` | Active Spring profile |
| `UAL_PORT` | Application port |
| `RABBITMQ_HOST` | RabbitMQ host |
| `RABBITMQ_PORT` | RabbitMQ port |
| `RABBITMQ_USERNAME` | RabbitMQ username |
| `RABBITMQ_PASSWORD` | RabbitMQ password |
| `SECURITY_REDIS_HOST` | Redis host |
| `SECURITY_REDIS_PORT` | Redis port |
| `JWT_RSA_PUBLIC_KEY` | JWT public key |
| `OAUTH_ISSUER` | OAuth issuer |

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

[License information]
