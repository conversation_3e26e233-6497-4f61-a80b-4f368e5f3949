package com.veefin.ual.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.sentry.Sentry;

@Configuration
public class RabbitMqConfig {
    private static final Logger logger = LoggerFactory.getLogger(RabbitMqConfig.class);

    @Value("${rabbitmq.queue.name}")
    private String queue;

    @Value("${rabbitmq.exchange.name}")
    private String exchange;

    @Value("${rabbitmq.routing.key}")
    private String routingKey;

    @Bean
    public Queue queue(){
        // Create a durable queue that won't be deleted when the server restarts
        Queue q = new org.springframework.amqp.core.Queue(queue, true, false, false);
        logger.info("Creating queue: {}", queue);
        return q;
    }

    @Bean
    public DirectExchange exchange() {
        // Create a durable exchange that won't be deleted when the server restarts
        DirectExchange ex = new DirectExchange(exchange, true, false);
        logger.info("Creating exchange: {}", exchange);
        return ex;
    }

    @Bean
    public Binding binding(){
        // Create a binding between the queue and exchange with the specified routing key
        Binding b = BindingBuilder.bind(queue()).to(exchange()).with(routingKey);
        logger.info("Creating binding between queue: {} and exchange: {} with routing key: {}",
                queue, exchange, routingKey);
        return b;
    }

    @Bean
    public RabbitAdmin rabbitAdmin(@Qualifier("rabbitConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitAdmin admin = new RabbitAdmin(connectionFactory);
        // Set to true to ensure the RabbitAdmin declares queues, exchanges, and bindings on startup
        admin.setAutoStartup(true);
        // Ignore declaration exceptions to allow the application to continue even if queue properties don't match
        admin.setIgnoreDeclarationExceptions(true);

        // Log that we're creating the RabbitAdmin
        logger.info("Creating RabbitAdmin with queue: {}, exchange: {}, routing key: {}", queue, exchange, routingKey);

        // Explicitly declare the queue, exchange, and binding
        try {
            // Create the main exchange if it doesn't exist
            DirectExchange ex = exchange();
            admin.declareExchange(ex);
            logger.info("Exchange declared: {}", ex.getName());

            // Create the main queue if it doesn't exist
            Queue q = queue();
            try {
                admin.declareQueue(q);
                logger.info("Queue declared: {}", q.getName());
            } catch (Exception queueEx) {
                // Log the exception but continue since we've set ignoreDeclarationExceptions=true
                logger.warn("Queue declaration failed, but continuing: {} - {}", q.getName(), queueEx.getMessage());
                logger.debug("Queue declaration exception details", queueEx);
            }

            // Create the main binding if it doesn't exist
            Binding b = binding();
            admin.declareBinding(b);
            logger.info("Binding declared between {} and {} with routing key {}",
                    queue, exchange, routingKey);
        } catch (Exception e) {
            logger.error("Failed to declare RabbitMQ resources", e);
        }

        return admin;
    }

    @Bean
    public MessageConverter converter(){
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(@Qualifier("rabbitConnectionFactory") ConnectionFactory connectionFactory){
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(converter());

        // Set mandatory flag for better error handling
        rabbitTemplate.setMandatory(true);

        // Set longer timeouts to prevent ACK timeouts
        rabbitTemplate.setReplyTimeout(60000); // 60 seconds

        // Note: setConfirmTimeout, setBatchSize, and setBufferLimit methods are not available in this version
        // These settings are now configured in application properties

        // Configure for reliable delivery
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                // Log and send to Sentry
                String errorMessage = "Message was not confirmed by broker: " + cause;
                logger.error(errorMessage);

                // Try to recreate the queue and binding if confirmation fails
                logger.warn("Message confirmation failed. Attempting to recreate RabbitMQ resources.");
                try {
                    RabbitAdmin admin = rabbitAdmin(connectionFactory);

                    // Recreate resources
                    admin.declareExchange(exchange());

                    // Try to declare queue but don't fail if it exists with different properties
                    try {
                        admin.declareQueue(queue());
                    } catch (Exception queueEx) {
                        logger.warn("Queue declaration failed during recovery, but continuing: {} - {}",
                                queue, queueEx.getMessage());
                    }

                    admin.declareBinding(binding());

                    logger.info("Resources recreated after confirmation failure");
                } catch (Exception e) {
                    logger.error("Failed to recreate RabbitMQ resources after confirmation failure", e);
                }

                Sentry.captureMessage(errorMessage);

                // If you have correlation data, you can include it
                if (correlationData != null && correlationData.getId() != null) {
                    Sentry.setExtra("correlationId", correlationData.getId());
                }
            }
        });

        // Handle returned messages (when they can't be routed to a queue)
        rabbitTemplate.setReturnsCallback(returned -> {
            String errorMessage = String.format(
                    "Message returned: Exchange: %s, RoutingKey: %s, ReplyCode: %d, ReplyText: %s",
                    returned.getExchange(),
                    returned.getRoutingKey(),
                    returned.getReplyCode(),
                    returned.getReplyText()
            );

            logger.error(errorMessage);

            // If we get a NO_ROUTE error, try to create the queue and binding
            if (returned.getReplyCode() == 312) { // NO_ROUTE
                logger.warn("Detected NO_ROUTE error. Queue or binding might be missing. Attempting to recreate resources.");
                try {
                    // Get the RabbitAdmin and explicitly declare resources
                    RabbitAdmin admin = rabbitAdmin(connectionFactory);

                    // Recreate resources
                    admin.declareExchange(exchange());

                    // Try to declare queue but don't fail if it exists with different properties
                    try {
                        admin.declareQueue(queue());
                    } catch (Exception queueEx) {
                        logger.warn("Queue declaration failed during NO_ROUTE recovery, but continuing: {} - {}",
                                queue, queueEx.getMessage());
                    }

                    admin.declareBinding(binding());

                    logger.info("Resources recreated successfully");
                } catch (Exception e) {
                    logger.error("Failed to recreate RabbitMQ resources", e);
                }
            }

            // Capture in Sentry with additional context
            Sentry.withScope(scope -> {
                scope.setExtra("exchange", returned.getExchange());
                scope.setExtra("routingKey", returned.getRoutingKey());
                scope.setExtra("replyText", returned.getReplyText());

                // Add message content if needed (be careful with sensitive data)
                if (returned.getMessage() != null && returned.getMessage().getBody() != null) {
                    try {
                        String messageBody = new String(returned.getMessage().getBody());
                        scope.setExtra("messageBody", messageBody);
                    } catch (Exception e) {
                        scope.setExtra("messageBodyError", "Could not convert message body to string");
                    }
                }

                Sentry.captureMessage("RabbitMQ message returned (undeliverable)");
            });
        });

        return rabbitTemplate;
    }

    // Customize connection factory for high throughput and reliability
    @Bean(name = "rabbitConnectionFactory")
    public ConnectionFactory rabbitConnectionFactory(
            @Value("${spring.rabbitmq.host}") String host,
            @Value("${spring.rabbitmq.port}") int port,
            @Value("${spring.rabbitmq.username}") String username,
            @Value("${spring.rabbitmq.password}") String password,
            @Value("${spring.rabbitmq.virtual-host:/}") String virtualHost) {

        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);

        // Connection pooling settings
        connectionFactory.setCacheMode(CachingConnectionFactory.CacheMode.CONNECTION);
        connectionFactory.setConnectionCacheSize(5); // Number of connections to cache
        connectionFactory.setChannelCacheSize(25); // Number of channels per connection

        // Set connection timeout
        connectionFactory.setConnectionTimeout(30000); // 30 seconds

        // Set channel checkout timeout
        connectionFactory.setChannelCheckoutTimeout(10000); // 10 seconds

        // Note: setRequestedHeartbeat is deprecated in newer versions
        // This is now configured in application properties

        // Enable publisher confirms with CORRELATED type for better performance
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);

        // Log connection factory settings
        logger.info("Configured RabbitMQ connection factory with host: {}, port: {}, virtualHost: {}, "
                + "connectionCacheSize: {}, channelCacheSize: {}, connectionTimeout: {} ms",
                host, port, virtualHost, 5, 25, 30000);

        return connectionFactory;
    }
}