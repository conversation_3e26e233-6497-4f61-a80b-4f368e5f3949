package com.veefin.ual.utilities;

import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;

public class EventLogSpecification {
    public static Specification<EventLog> filterBySearch(String search) {
        return (root, query, criteriaBuilder) -> {
            if (search == null || search.trim().isEmpty()) {
                return null;
            }
            Subquery<String> subquery = query.subquery(String.class);
            Root<UserActivitySession> session = subquery.from(UserActivitySession.class);
            Expression<String> sessionIdStr = criteriaBuilder.function(Constant.FUNCTION_STR, String.class, session.get(Constant.FIELD_ID));
            subquery.select(sessionIdStr);
            Predicate namePredicate = criteriaBuilder.like(criteriaBuilder.lower(session.get(Constant.FIELD_NAME)), Constant.WILDCARD + search.toLowerCase() + Constant.WILDCARD);
            Predicate emailPredicate = criteriaBuilder.like(criteriaBuilder.lower(session.get(Constant.FIELD_EMAIL)), Constant.WILDCARD + search.toLowerCase() + Constant.WILDCARD);
            subquery.where(criteriaBuilder.or(namePredicate, emailPredicate));
            return root.get(Constant.FIELD_USER_ACTIVITY_SESSION_ID).in(subquery);
        };
    }

    public static Specification<EventLog> filterByModule(String module) {
        return (root, query, criteriaBuilder) ->
                (module == null || module.trim().isEmpty()) ? null :
                        criteriaBuilder.like(criteriaBuilder.lower(root.get(Constant.FIELD_MODULE)), Constant.WILDCARD + module.toLowerCase() + Constant.WILDCARD);
    }

    public static Specification<EventLog> filterByAction(String action) {
        return (root, query, criteriaBuilder) ->
                (action == null || action.trim().isEmpty()) ? null :
                        criteriaBuilder.like(criteriaBuilder.lower(root.get(Constant.FIELD_ACTION)), Constant.WILDCARD + action.toLowerCase() + Constant.WILDCARD);
    }

    public static Specification<EventLog> filterBySystemName(String systemName) {
        return (root, query, criteriaBuilder) ->
                (systemName == null || systemName.trim().isEmpty()) ? null :
                        criteriaBuilder.like(criteriaBuilder.lower(root.get(Constant.FIELD_SYSTEM_NAME)), Constant.WILDCARD + systemName.toLowerCase() + Constant.WILDCARD);
    }

    public static Specification<EventLog> filterByTimeRange(LocalDateTime startDate, LocalDateTime endDate) {
        return (root, query, criteriaBuilder) -> {
            if (startDate != null && endDate != null) {
                return criteriaBuilder.between(root.get(Constant.FIELD_EVENT_AT), startDate, endDate);
            } else if (startDate != null) {
                return criteriaBuilder.greaterThanOrEqualTo(root.get(Constant.FIELD_EVENT_AT), startDate);
            } else if (endDate != null) {
                return criteriaBuilder.lessThanOrEqualTo(root.get(Constant.FIELD_EVENT_AT), endDate);
            }
            return null;
        };
    }
}
