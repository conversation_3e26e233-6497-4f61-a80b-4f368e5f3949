definitions:
  steps:
    - step: &build
        name: Build
        runs-on:
          - self.hosted
          - linux.shell
        script:
          - chmod +x build.sh
          - ./build.sh
pipelines:
  default:
    - step: *build
  branches:
    dev:
      - step: *build
      - step:
          deployment: development
          name : Deploy to dev
          trigger: manual
          runs-on:
            - self.hosted
            - linux.shell
          script:
          - chmod +x deploy.sh          
          - ./deploy.sh
    QA:
      - step: *build
      - step:
          deployment: QA
          name : Deploy to QA
          trigger: manual
          runs-on:
            - self.hosted
            - linux.shell
          script:
          - chmod +x deploy.sh          
          - ./deploy.sh
    UAT:
      - step: *build
      - step:
          deployment: UAT
          name : Deploy to UAT
          trigger: manual
          runs-on:
            - self.hosted
            - linux.shell
          script:
          - chmod +x deploy.sh                    
          - ./deploy.sh