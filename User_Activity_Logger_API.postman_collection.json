{"info": {"_postman_id": "e5a7b8c9-d0f1-4e2a-b3c4-5a6b7c8d9e0f", "name": "User Activity Logger API", "description": "A comprehensive collection for the User Activity Logger API, which tracks and queries user activity events across multiple systems.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Session Management", "description": "Endpoints for managing user activity sessions", "item": [{"name": "Identify User Session", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text", "description": "JWT token for authentication"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"uuid\": \"user-uuid-123\",\n    \"system_name\": \"SCF\",\n    \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n    \"client_ip\": \"***********\",\n    \"source\": \"WEB\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/identify", "host": ["{{base_url}}"], "path": ["v1", "identify"]}, "description": "Registers a new user session and returns a session ID that must be used in subsequent API calls."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"uuid\": \"user-uuid-123\",\n    \"system_name\": \"SCF\",\n    \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n    \"client_ip\": \"***********\",\n    \"source\": \"WEB\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/identify", "host": ["{{base_url}}"], "path": ["v1", "identify"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": \"200\",\n    \"message\": \"Session registered successfully\",\n    \"data\": \"550e8400-e29b-41d4-a716-************\"\n}"}]}, {"name": "Search User Sessions", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": null,\n    \"email\": null,\n    \"user_uuid\": null,\n    \"client_ip\": null,\n    \"user_agent\": null,\n    \"system_name\": \"SCF\",\n    \"from_date\": \"2023-01-01\",\n    \"to_date\": \"2023-12-31\",\n    \"search\": \"\",\n    \"page\": 0,\n    \"size\": 20\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/user-activity-sessions/search", "host": ["{{base_url}}"], "path": ["v1", "user-activity-sessions", "search"]}, "description": "Retrieves a paginated list of user activity sessions with advanced filtering options."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": null,\n    \"email\": null,\n    \"user_uuid\": null,\n    \"client_ip\": null,\n    \"user_agent\": null,\n    \"system_name\": \"SCF\",\n    \"from_date\": \"2023-01-01\",\n    \"to_date\": \"2023-12-31\",\n    \"search\": \"\",\n    \"page\": 0,\n    \"size\": 20\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/user-activity-sessions/search", "host": ["{{base_url}}"], "path": ["v1", "user-activity-sessions", "search"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": \"200\",\n    \"message\": \"Pagination for sessions run successfully\",\n    \"data\": {\n        \"total_count\": 2,\n        \"sessions\": [\n            {\n                \"id\": \"550e8400-e29b-41d4-a716-************\",\n                \"name\": \"<PERSON>\",\n                \"email\": \"<EMAIL>\",\n                \"user_uuid\": \"user-uuid-123\",\n                \"system_name\": \"SCF\",\n                \"user_agent\": \"Mozilla/5.0\",\n                \"client_ip\": \"***********\",\n                \"source\": \"WEB\"\n            },\n            {\n                \"id\": \"550e8400-e29b-41d4-a716-************\",\n                \"name\": \"<PERSON>\",\n                \"email\": \"<EMAIL>\",\n                \"user_uuid\": \"user-uuid-456\",\n                \"system_name\": \"SCF\",\n                \"user_agent\": \"Mozilla/5.0\",\n                \"client_ip\": \"***********\",\n                \"source\": \"MOBILE\"\n            }\n        ]\n    }\n}"}]}]}, {"name": "Event Management", "description": "Endpoints for managing user activity events", "item": [{"name": "Log User Event (Async)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"module\": \"DASHBOARD\",\n    \"action\": \"VIEW\",\n    \"user_activity_session_id\": \"550e8400-e29b-41d4-a716-************\",\n    \"system_name\": \"SCF\",\n    \"client_ip\": \"***********\",\n    \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n    \"event_at\": \"2023-06-15T14:30:00\",\n    \"properties\": {\n        \"page\": \"home\",\n        \"section\": \"summary\",\n        \"duration\": 120\n    },\n    \"message\": \"User viewed dashboard summary\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/events", "host": ["{{base_url}}"], "path": ["v1", "events"]}, "description": "Logs a user activity event asynchronously using virtual threads."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"module\": \"DASHBOARD\",\n    \"action\": \"VIEW\",\n    \"user_activity_session_id\": \"550e8400-e29b-41d4-a716-************\",\n    \"system_name\": \"SCF\",\n    \"client_ip\": \"***********\",\n    \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n    \"event_at\": \"2023-06-15T14:30:00\",\n    \"properties\": {\n        \"page\": \"home\",\n        \"section\": \"summary\",\n        \"duration\": 120\n    },\n    \"message\": \"User viewed dashboard summary\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/events", "host": ["{{base_url}}"], "path": ["v1", "events"]}}, "status": "Accepted", "code": 202, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": \"202\",\n    \"message\": \"Event processing started asynchronously\",\n    \"data\": \"550e8400-e29b-41d4-a716-446655440002\"\n}"}]}, {"name": "Search User Events", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"module\": null,\n    \"action\": null,\n    \"user_activity_session_id\": null,\n    \"user_name\": null,\n    \"user_email\": null,\n    \"client_ip\": null,\n    \"user_agent\": null,\n    \"system_name\": \"SCF\",\n    \"search\": \"\",\n    \"page\": 0,\n    \"size\": 20,\n    \"from_date\": \"2023-01-01\",\n    \"to_date\": \"2023-12-31\",\n    \"exact_match\": false,\n    \"match_all_terms\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/events/search", "host": ["{{base_url}}"], "path": ["v1", "events", "search"]}, "description": "Searches for user events with advanced filtering options."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"module\": null,\n    \"action\": null,\n    \"user_activity_session_id\": null,\n    \"user_name\": null,\n    \"user_email\": null,\n    \"client_ip\": null,\n    \"user_agent\": null,\n    \"system_name\": \"SCF\",\n    \"search\": \"\",\n    \"page\": 0,\n    \"size\": 20,\n    \"from_date\": \"2023-01-01\",\n    \"to_date\": \"2023-12-31\",\n    \"exact_match\": false,\n    \"match_all_terms\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/v1/events/search", "host": ["{{base_url}}"], "path": ["v1", "events", "search"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": \"200\",\n    \"message\": \"Pagination for events run successfully\",\n    \"data\": {\n        \"total_count\": 2,\n        \"events\": [\n            {\n                \"id\": \"550e8400-e29b-41d4-a716-446655440002\",\n                \"module\": \"DASHBOARD\",\n                \"action\": \"VIEW\",\n                \"user_activity_sessionId\": \"550e8400-e29b-41d4-a716-************\",\n                \"system_name\": \"SCF\",\n                \"properties\": \"{\\\"page\\\":\\\"home\\\",\\\"section\\\":\\\"summary\\\",\\\"duration\\\":120}\",\n                \"client_ip\": \"***********\",\n                \"user_agent\": \"Mozilla/5.0\",\n                \"event_at\": \"15-06-2023 14:30:00\",\n                \"message\": \"User viewed dashboard summary\",\n                \"user_name\": \"<PERSON>\",\n                \"user_email\": \"<EMAIL>\",\n                \"user_uuid\": \"user-uuid-123\"\n            },\n            {\n                \"id\": \"550e8400-e29b-41d4-a716-************\",\n                \"module\": \"PROFILE\",\n                \"action\": \"UPDATE\",\n                \"user_activity_sessionId\": \"550e8400-e29b-41d4-a716-************\",\n                \"system_name\": \"SCF\",\n                \"properties\": \"{\\\"field\\\":\\\"email\\\",\\\"old_value\\\":\\\"<EMAIL>\\\",\\\"new_value\\\":\\\"<EMAIL>\\\"}\",\n                \"client_ip\": \"***********\",\n                \"user_agent\": \"Mozilla/5.0\",\n                \"event_at\": \"16-06-2023 10:15:00\",\n                \"message\": \"User updated profile\",\n                \"user_name\": \"Jane Smith\",\n                \"user_email\": \"<EMAIL>\",\n                \"user_uuid\": \"user-uuid-456\"\n            }\n        ]\n    }\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:7082/ual", "type": "string", "description": "Base URL for the User Activity Logger API"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string", "description": "JWT token for authentication"}]}