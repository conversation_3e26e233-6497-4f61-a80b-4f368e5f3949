spring.application.name=ual
server.servlet.context-path=/ual
spring.profiles.active=${SPRING_PROFILES_ACTIVE:local}
server.port=${UAL_PORT}
# The active profile can be set via environment variable or command line
# If not set, the application will use default configuration

# Allow bean definition overriding to handle conflicts between Redis and RabbitMQ connection factories
spring.main.allow-bean-definition-overriding=true

# Enable virtual threads for Spring Boot
spring.threads.virtual.enabled=true

## Database configuration ##
spring.datasource.url=${DATASOURCE_URL}
spring.datasource.username=${USERNAME}
spring.datasource.password=${PASSWORD}
spring.jpa.hibernate.ddl-auto=validate
spring.datasource.driver-class-name=${DRIVER_CLASS_NAME}
spring.jpa.database-platform=${DATABASE_PLATFORM}
spring.jpa.properties.hibernate.dialect=${DATABASE_PLATFORM}

# Optimize batch operations
spring.jpa.properties.hibernate.jdbc.batch_size=30
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
# Connection pool optimization
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=10
#spring.datasource.hikari.idle-timeout=30000
#spring.datasource.hikari.connection-timeout=20000

## Flyway configuration ##
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration
spring.flyway.table=flyway_schema_history
spring.flyway.baseline-version=0
spring.flyway.validate-on-migrate=true
spring.flyway.repair-on-migrate=true
spring.flyway.out-of-order=true

## RabbitMQ configuration ##
rabbitmq.queue.name=${USER_EVENTS_QUEUE}
rabbitmq.exchange.name=${USER_EVENTS_EXCHANGE}
rabbitmq.routing.key=${USER_ROUTING_KEY}
add.event.db=${IS_DB_EVENT}

## Spring Redis configuration ##
spring.redis.host=${SECURITY_REDIS_HOST}
spring.redis.port=${SECURITY_REDIS_PORT}
SECURITY_REDIS_HOST=${SECURITY_REDIS_HOST}
SECURITY_REDIS_PORT=${SECURITY_REDIS_PORT}

## JWT Authentication Keys ##
jwt.rsa-public-key=${JWT_RSA_PUBLIC_KEY}

oauth.server.issuer=${OAUTH_ISSUER}

#SENTRY CONFIG
sentry.enabled=${SENTRY_ENABLED:true}
sentry.dsn=${SENTRY_DSN}
sentry.traces-sample-rate=${SENTRY_RATE:1.0}
sentry.attach-stacktrace=${SENTRY_TRACE:true}
sentry.environment=${spring.profiles.active:local}
sentry.release=${spring.application.name}@1.0.0
sentry.in-app-includes=com.veefin.ual
sentry.send-default-pii=true

## Rabbit MQ configuration
spring.rabbitmq.host=${RABBITMQ_HOST}
spring.rabbitmq.port=${RABBITMQ_PORT}
spring.rabbitmq.username=${RABBITMQ_USERNAME}
spring.rabbitmq.password=${RABBITMQ_PASSWORD}

## Logging configuration ##
logging.level.root=INFO
logging.level.com.veefin.ual.controller=INFO
logging.level.com.veefin.ual.service=INFO
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO
logging.level.io.opentelemetry=INFO

## OpenTelemetry Configuration ##
otel.service.name=${spring.application.name}
otel.traces.exporter=otlp
otel.metrics.exporter=otlp
otel.logs.exporter=otlp
otel.exporter.otlp.endpoint=${OTEL_EXPORTER_OTLP_ENDPOINT}
otel.exporter.otlp.traces.endpoint=${OTEL_EXPORTER_OTLP_TRACES_ENDPOINT}
otel.exporter.otlp.protocol=http/protobuf
otel.propagators=tracecontext,baggage
otel.traces.sampler=always_on
otel.instrumentation.http.client.capture-request-headers=content-type,user-agent
otel.instrumentation.http.client.capture-response-headers=content-type,content-length
otel.instrumentation.http.server.capture-request-headers=content-type,user-agent
otel.instrumentation.http.server.capture-response-headers=content-type,content-length

## Actuator configuration ##
# Expose all actuator endpoints
management.endpoints.web.exposure.include=*
# Enable detailed health information
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
# Enable environment, Java, and OS info
management.info.env.enabled=true
management.info.java.enabled=true
management.info.os.enabled=true
# Set application info properties
info.app.name=${spring.application.name}
info.app.description=User Activity Logger Service
info.app.version=1.0.0
info.app.environment=${spring.profiles.active:none}
# Prometheus metrics
management.prometheus.metrics.export.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.slo.http.server.requests=1ms,5ms,10ms,50ms,100ms,200ms,500ms,1s,5s
management.metrics.tags.application=${spring.application.name}
