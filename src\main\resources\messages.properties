error.generic=An unexpected error occurred
error.validation=Validation error
error.resource.notfound={0} with ID {1} not found
error.business.insufficientFunds=Insufficient funds. Required: {0}, Available: {1}
error.business.invalidAccountStatus=Account is not in a valid state: {0}
error.security.unauthorized=Authentication required
error.security.forbidden=You do not have permission to access this resource