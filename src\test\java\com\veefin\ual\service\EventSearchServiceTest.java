package com.veefin.ual.service;

import com.veefin.ual.config.TestConfig;
import com.veefin.ual.dto.EventSearchRequestDTO;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.UserActivityEventPaginatedDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import com.veefin.ual.repository.EventLogRepository;
import com.veefin.ual.repository.UserActivitySessionRepository;
import com.veefin.ual.service.impl.EventSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class EventSearchServiceTest {

    @Mock
    private EventLogRepository eventLogRepository;

    @Mock
    private UserActivitySessionRepository userActivitySessionRepository;

    @InjectMocks
    private EventSearchServiceImpl eventSearchService;

    private EventLog eventLog;
    private UserActivitySession userActivitySession;
    private EventSearchRequestDTO requestDto;
    private String sessionId;

    @BeforeEach
    void setUp() {
        // Initialize test data
        sessionId = UUID.randomUUID().toString();

        userActivitySession = new UserActivitySession();
        userActivitySession.setId(sessionId);
        userActivitySession.setEmail("<EMAIL>");
        userActivitySession.setName("Test User");
        userActivitySession.setUserUuid("test-uuid");
        userActivitySession.setSystemName("TEST");
        userActivitySession.setUserAgent("Test Agent");
        userActivitySession.setClientIp("127.0.0.1");
        userActivitySession.setSource("WEB");
        userActivitySession.setCreatedAt(LocalDateTime.now());

        eventLog = new EventLog();
        eventLog.setId(UUID.randomUUID().toString());
        eventLog.setUserActivitySessionId(sessionId);
        eventLog.setModule("TEST_MODULE");
        eventLog.setAction("TEST_ACTION");
        eventLog.setSystemName("TEST");
        eventLog.setCreatedAt(LocalDateTime.now());
        eventLog.setEventAt(LocalDateTime.now());

        requestDto = new EventSearchRequestDTO();
        requestDto.setPage(0);
        requestDto.setSize(10);
        requestDto.setModule("TEST_MODULE");
        requestDto.setAction("TEST_ACTION");
        requestDto.setSystemName("TEST");
    }

    @Test
    void testSearchEvents() {
        // Create test data
        List<EventLog> eventLogs = new ArrayList<>();
        eventLogs.add(eventLog);
        Page<EventLog> page = new PageImpl<>(eventLogs);

        // Mock repository behavior
        when(eventLogRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(userActivitySessionRepository.findAllById(anyList())).thenReturn(List.of(userActivitySession));

        // Call the service method
        ResponseDTO<?> responseDTO = eventSearchService.searchEvents(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());

        // Verify repository was called
        verify(eventLogRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(userActivitySessionRepository, times(1)).findAllById(anyList());
    }

    @Test
    void testSearchEventsWithSize10() {
        // Create test data with exactly 10 events
        List<EventLog> eventLogs = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            EventLog event = new EventLog();
            event.setId(UUID.randomUUID().toString());
            event.setUserActivitySessionId(sessionId);
            event.setModule("TEST_MODULE_" + i);
            event.setAction("TEST_ACTION_" + i);
            event.setSystemName("TEST");
            event.setCreatedAt(LocalDateTime.now());
            event.setEventAt(LocalDateTime.now());
            eventLogs.add(event);
        }
        Page<EventLog> page = new PageImpl<>(eventLogs);

        // Set size to 10
        requestDto.setSize(10);

        // Mock repository behavior
        when(eventLogRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(userActivitySessionRepository.findAllById(anyList())).thenReturn(List.of(userActivitySession));

        // Call the service method
        ResponseDTO<?> responseDTO = eventSearchService.searchEvents(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("Pagination for events run successfully", responseDTO.getMessage());

        // Verify that the pageable was created with size 10
        verify(eventLogRepository, times(1)).findAll(any(Specification.class), argThat((Pageable pageable) ->
            pageable.getPageSize() == 10
        ));
    }

    @Test
    void testSearchEventsWithEmptyResults() {
        // Mock repository behavior
        when(eventLogRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(Page.empty());

        // Call the service method
        ResponseDTO<?> responseDTO = eventSearchService.searchEvents(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("No events found", responseDTO.getMessage());

        // Verify repository was called
        verify(eventLogRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(userActivitySessionRepository, never()).findAllById(anyList());
    }

    @Test
    void testSearchEventsWithNullPage() {
        // Mock repository behavior to return null
        when(eventLogRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(null);

        // Call the service method
        ResponseDTO<?> responseDTO = eventSearchService.searchEvents(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("No events found", responseDTO.getMessage());

        // Verify repository was called
        verify(eventLogRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(userActivitySessionRepository, never()).findAllById(anyList());
    }

    @Test
    void testSearchEventsWithPageBeyondAvailableData() {
        // Create test data - simulate 5 total elements but requesting page 1 with size 10
        List<EventLog> eventLogs = new ArrayList<>(); // Empty content for page 1
        Page<EventLog> page = new PageImpl<>(eventLogs, PageRequest.of(1, 10), 5); // 5 total elements, page 1, size 10

        // Set request to page 1 (second page) with size 10
        requestDto.setPage(1);
        requestDto.setSize(10);

        // Mock repository behavior
        when(eventLogRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // Call the service method
        ResponseDTO<?> responseDTO = eventSearchService.searchEvents(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("Requested page is beyond available data", responseDTO.getMessage());

        // Verify that total count is preserved
        UserActivityEventPaginatedDTO paginatedDTO = (UserActivityEventPaginatedDTO) responseDTO.getData();
        assertNotNull(paginatedDTO);
        assertEquals(5, paginatedDTO.getTotalCount()); // Should preserve total count
        //assertTrue(paginatedDTO.getEvents().isEmpty()); // But events list should be empty

        // Verify repository was called
        verify(eventLogRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(userActivitySessionRepository, never()).findAllById(anyList()); // Should not fetch sessions for empty results
    }

    @Test
    void testSearchEventsWithSizeLargerThanTotalRecords() {
        // Create test data - simulate 3 total records but requesting size 10
        List<EventLog> eventLogs = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            EventLog event = new EventLog();
            event.setId(UUID.randomUUID().toString());
            event.setUserActivitySessionId(sessionId);
            event.setModule("TEST_MODULE_" + i);
            event.setAction("TEST_ACTION_" + i);
            event.setSystemName("TEST");
            event.setCreatedAt(LocalDateTime.now());
            event.setEventAt(LocalDateTime.now());
            eventLogs.add(event);
        }

        // Create page with 3 elements, page 0, size 10 - this should return all 3 records
        Page<EventLog> page = new PageImpl<>(eventLogs, PageRequest.of(0, 10), 3);

        // Set request to page 0 with size 10
        requestDto.setPage(0);
        requestDto.setSize(10);

        // Mock repository behavior
        when(eventLogRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);
        when(userActivitySessionRepository.findAllById(anyList())).thenReturn(List.of(userActivitySession));

        // Call the service method
        ResponseDTO<?> responseDTO = eventSearchService.searchEvents(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("Pagination for events run successfully", responseDTO.getMessage());

        // Verify that all 3 records are returned
        UserActivityEventPaginatedDTO paginatedDTO = (UserActivityEventPaginatedDTO) responseDTO.getData();
        assertNotNull(paginatedDTO);
        assertEquals(3, paginatedDTO.getTotalCount()); // Should show total count as 3
        assertEquals(3, paginatedDTO.getEvents().size()); // Should return all 3 events

        // Verify repository was called
        verify(eventLogRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(userActivitySessionRepository, times(1)).findAllById(anyList());
    }
}
