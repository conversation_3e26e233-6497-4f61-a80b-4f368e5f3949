# RabbitMQ Advanced Configuration

# Connection settings
spring.rabbitmq.connection-timeout=30000
spring.rabbitmq.requested-heartbeat=30
spring.rabbitmq.cache.connection.mode=CONNECTION
spring.rabbitmq.cache.connection.size=5
spring.rabbitmq.cache.channel.size=25
spring.rabbitmq.cache.channel.checkout-timeout=10000

# Publisher settings
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.template.mandatory=true
spring.rabbitmq.template.receive-timeout=60000
spring.rabbitmq.template.reply-timeout=60000
spring.rabbitmq.template.retry.enabled=true
spring.rabbitmq.template.retry.initial-interval=1000
spring.rabbitmq.template.retry.max-attempts=3
spring.rabbitmq.template.retry.max-interval=10000
spring.rabbitmq.template.retry.multiplier=1.0

# Consumer settings
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.initial-interval=1000
spring.rabbitmq.listener.simple.retry.max-attempts=3
spring.rabbitmq.listener.simple.retry.max-interval=10000
spring.rabbitmq.listener.simple.retry.multiplier=1.0
spring.rabbitmq.listener.simple.default-requeue-rejected=false
spring.rabbitmq.listener.simple.prefetch=10
spring.rabbitmq.listener.simple.concurrency=5
spring.rabbitmq.listener.simple.max-concurrency=10
