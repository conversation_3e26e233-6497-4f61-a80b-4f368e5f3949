package com.veefin.ual.utilities;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.veefin.ual.dto.PaginationRequestDTO;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import com.veefin.ual.exceptionhandler.ResourceNotFound;
import com.veefin.workflow.wrapper.security.redis.UserLogin;

import io.sentry.Sentry;
import jakarta.servlet.http.HttpServletRequest;

@Component
public class UserActivitySessionUtil {

	Logger logger = LoggerFactory.getLogger(UserActivitySessionUtil.class);

	public UserActivitySession convertDtoTOUserActivitySession(UserActivitySessionDTO userActivitySessionDTO) {
		UserActivitySession userActivitySession = new UserActivitySession();

		BeanUtils.copyProperties(userActivitySessionDTO, userActivitySession);

		userActivitySession.setUserUuid(userActivitySessionDTO.getUuid());

		return userActivitySession;
	}

	public EventLog convertDtoToEventLog(UserActivityEventsDTO userActivityEventsDTO) {
		EventLog eventLog = new EventLog();

		BeanUtils.copyProperties(userActivityEventsDTO, eventLog);

		eventLog.setProperties(userActivityEventsDTO.getProperties().toString());

		return eventLog;
	}

	public UserActivityEventsDTO convertEventLogToDto(EventLog eventLog) {
		UserActivityEventsDTO dto = new UserActivityEventsDTO();

		BeanUtils.copyProperties(eventLog, dto);

		// Convert the properties string to JsonNode
		ObjectMapper mapper = new ObjectMapper();
		try {
			JsonNode propertiesNode = mapper.readTree(eventLog.getProperties());
			dto.setProperties(propertiesNode);
		} catch (IOException e) {
			// Handle exception as needed. For now, we can log and set properties to null.
			Sentry.captureException(e);

			dto.setProperties(null);
			logger.info("Error: IOException Properties value : {}", e.getMessage());

		}

		// If you have additional fields (e.g., message, module), set them here.
		dto.setMessage(eventLog.getMessage());
		dto.setModule(eventLog.getModule());

		return dto;
	}

	public static void getFieldFromToken(UserActivitySessionDTO userActivitySessionDTO, HttpServletRequest request)
			throws ResourceNotFound {
		if (ObjectUtils.isEmpty(UserLogin.getLoggedInUserId())) {
			//throw new ResourceNotFound("User Login uuid is empty or null");
		}
		if (!ObjectUtils.isEmpty(UserLogin.getLoggedInUserName())) {
			userActivitySessionDTO.setName(UserLogin.getLoggedInUserName());
		}
		// client ip for dto first
		//userActivitySessionDTO.setUuid(UserLogin.getLoggedInUserId().toString());
		if (userActivitySessionDTO.getClientIp() == null || userActivitySessionDTO.getClientIp().isEmpty()) {
			String ip = request.getHeader("X-Forwarded-For");
			if (ip == null || ip.isEmpty()) {
				ip = request.getHeader("X-Real-IP");
			}
			if (ip == null || ip.isEmpty()) {
				ip = request.getRemoteAddr();
			}
			userActivitySessionDTO.setClientIp(ip != null ? ip.split(",")[0].trim() : "Unknown");
		}
	}

	public static void getFieldFromTokenForEvents(UserActivityEventsDTO userActivityEventsDTO,
			HttpServletRequest request) {
		// if (!ObjectUtils.isEmpty(UserLogin.getLoggedInUserLocale())) {
		// userActivityEventsDTO.setSystemName(UserLogin.getLoggedInUserLocale());
		userActivityEventsDTO.setSystemName(userActivityEventsDTO.getSystemName());
		// }
		if (userActivityEventsDTO.getClientIp() == null || userActivityEventsDTO.getClientIp().isEmpty()) {
			String ip = request.getHeader("X-Forwarded-For");
			if (ip == null || ip.isEmpty()) {
				ip = request.getHeader("X-Real-IP");
			}
			if (ip == null || ip.isEmpty()) {
				ip = request.getRemoteAddr();
			}
			userActivityEventsDTO.setClientIp(ip != null ? ip.split(",")[0].trim() : "Unknown");
		}
	}

	public static void validatePaginationData(PaginationRequestDTO paginationRequestDTO, String sessionId)
			throws ResourceNotFound {
		if (paginationRequestDTO.getSystemName() == null || paginationRequestDTO.getSystemName().isEmpty()) {
			throw new ResourceNotFound(Constant.FIELD_SYSTEM_NAME, sessionId);
		}
	}
}
