package com.veefin.ual.aspect;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@Aspect
public class LoggingAspect {

    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);

    // Define a pointcut that matches all public methods in the com.veefin.ual package and sub-packages
    @Pointcut("execution(public * com.veefin.ual..*(..))")
    public void userActivityLoggerMethods() {}

    // Before advice - Logs method execution before it runs with timestamp
    @Before("userActivityLoggerMethods()")
    public void logBefore(JoinPoint joinPoint) {
        String timestamp = getCurrentTimestamp();
        logger.debug("{} - Executing method: {} with Arguments: {}", timestamp, joinPoint.getSignature().getName(), joinPoint.getArgs());
    }

    // After advice - Logs method execution after it finishes with timestamp
    @After("userActivityLoggerMethods()")
    public void logAfter(JoinPoint joinPoint) {
        String timestamp = getCurrentTimestamp();
        logger.debug("{} - Executed method: {}", timestamp, joinPoint.getSignature().getName());
    }

    // Around advice - Logs method execution with timestamps, calculates execution time
    @Around("userActivityLoggerMethods()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String timestampBefore = getCurrentTimestamp();
        logger.debug("{} - Starting method: {}", timestampBefore, joinPoint.getSignature().getName());

        long startTime = System.currentTimeMillis();

        Object result = joinPoint.proceed();

        long endTime = System.currentTimeMillis();
        String timestampAfter = getCurrentTimestamp();
        logger.debug("{} - Finished method: {} in {} ms", timestampAfter, joinPoint.getSignature().getName(), (endTime - startTime));

        return result;
    }

    // Helper method to get the current timestamp in a readable format
    private String getCurrentTimestamp() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        return LocalDateTime.now().format(formatter);
    }
}
