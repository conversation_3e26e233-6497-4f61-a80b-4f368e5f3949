package com.veefin.ual.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.veefin.ual.dto.EventSearchRequestDTO;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.UserActivityEventPaginatedDTO;
import com.veefin.ual.dto.UserActivityEventResponseDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import com.veefin.ual.repository.EventLogRepository;
import com.veefin.ual.repository.UserActivitySessionRepository;
import com.veefin.ual.service.EventSearchService;
import com.veefin.ual.service.SentryService;
import com.veefin.ual.specification.EventLogPaginationSpecification;

/**
 * Implementation of the EventSearchService interface.
 * Provides functionality for searching and retrieving event logs.
 */
@Service
public class EventSearchServiceImpl implements EventSearchService {

    private final EventLogRepository eventLogRepository;
    private final UserActivitySessionRepository userActivitySessionRepository;
    private final SentryService sentryService;
    private final Logger logger = LoggerFactory.getLogger(EventSearchServiceImpl.class);

    public EventSearchServiceImpl(EventLogRepository eventLogRepository,
                                UserActivitySessionRepository userActivitySessionRepository,
                                SentryService sentryService) {
        this.eventLogRepository = eventLogRepository;
        this.userActivitySessionRepository = userActivitySessionRepository;
        this.sentryService = sentryService;
    }

    @Override
    public ResponseDTO<?> searchEvents(EventSearchRequestDTO requestDto) {
        logger.debug(
                "searchEvents method starts with page: {}, size: {}, search: {}, module: {}, action: {}, systemName: {}",
                requestDto.getPage(), requestDto.getSize(), requestDto.getSearch(), requestDto.getModule(),
                requestDto.getAction(), requestDto.getSystemName());

        try {
            // Build pagination and specification
            Pageable pageable = PageRequest.of(requestDto.getPage(), requestDto.getSize(), Sort.by(Sort.Direction.DESC, "eventAt"));
            Specification<EventLog> spec = EventLogPaginationSpecification.fromDTO(requestDto);

            // Fetch paginated data
            Page<EventLog> page = eventLogRepository.findAll(spec, pageable);

            // Check if page is null
            if (page == null) {
                logger.warn("Null page result returned from repository");
                return new ResponseDTO<>(HttpStatus.OK.toString(), "No events found",
                        new UserActivityEventPaginatedDTO(0, List.of()));
            }

            // Get all event logs from the page
            List<EventLog> eventLogs = page.getContent();

            // Check if content is empty but total elements exist
            if (eventLogs.isEmpty()) {
                if (page.getTotalElements() == 0) {
                    logger.debug("No events found matching the search criteria");
                    return new ResponseDTO<>(HttpStatus.OK.toString(), "No events found",
                            new UserActivityEventPaginatedDTO(0, List.of()));
                } else {
                    logger.debug("Requested page {} is beyond available data. Total elements: {}, Total pages: {}",
                            requestDto.getPage(), page.getTotalElements(), page.getTotalPages());
                    return new ResponseDTO<>(HttpStatus.OK.toString(), "Requested page is beyond available data",
                            new UserActivityEventPaginatedDTO(page.getTotalElements(), List.of()));
                }
            }

            // Extract all session IDs
            List<String> sessionIds = eventLogs.stream()
                    .map(EventLog::getUserActivitySessionId)
                    .filter(id -> id != null)
                    .distinct()
                    .toList();

            // Fetch all sessions in a single query
            Map<String, UserActivitySession> sessionMap = new HashMap<>();
            if (!sessionIds.isEmpty()) {
                try {
                    List<UserActivitySession> sessions = userActivitySessionRepository.findAllById(sessionIds);
                    // Build a map of session ID to session object
                    for (UserActivitySession session : sessions) {
                        if (session != null && session.getId() != null) {
                            sessionMap.put(session.getId(), session);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("Error fetching sessions: {}", e.getMessage());
                    sentryService.captureException(e);
                }
            }

            // Create DTOs with session information
            List<UserActivityEventResponseDTO> dtoList = eventLogs.stream()
                    .map(eventLog -> {
                        String sessionId = eventLog.getUserActivitySessionId();
                        UserActivitySession session = sessionId != null ? sessionMap.get(sessionId) : null;
                        return new UserActivityEventResponseDTO(eventLog, session);
                    })
                    .toList();

            // Wrap in paginated DTO
            UserActivityEventPaginatedDTO paginatedDTO = new UserActivityEventPaginatedDTO(page.getTotalElements(),
                    dtoList);

            logger.debug("searchEvents method ends - total events fetched: {}", page.getTotalElements());
            return new ResponseDTO<>(HttpStatus.OK.toString(), "Pagination for events run successfully", paginatedDTO);

        } catch (Exception e) {
            sentryService.captureException(e);
            logger.error("Error fetching events", e);
            return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "Error fetching events", null);
        }
    }
}
