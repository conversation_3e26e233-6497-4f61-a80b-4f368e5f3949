echo "Setting environment variables..."
#MAVEN_VERSION='*******'
VERSION_TYPE='DEV'
BITBUCKET_COMMIT_SHORT="${BITBUCKET_COMMIT::7}"
MAVEN_FTP_USER=${MAVEN_FTP_USER}
MAVEN_FTP_HOST=${MAVEN_FTP_HOST}
MAVEN_BASE_PATH='/mount/veefinmaven/repository'
MAVEN_HTTP_URL='https://maven.veefin.in/core/com/veefin'
JAR_HOME_DIR='/home/<USER>/java-dev'
JAR_FILE_LOCATION='/activity_logger/activity-logger.jar'
JAVA_HOME=/usr/lib/jvm/jdk-21.0.4-oracle-x64
MAVEN_COM_VEEFIN_DIR='/home/<USER>/.m2/repository/com/veefin'

# Setting VERSION_SUFFIX
if [ ${BITBUCKET_BRANCH} == QA ]; then VERSION_TYPE=$STAGING_VERSION; VERSION_SUFFIX='-QA'; SSH_USER=$SCF4_QA_SSH_USER; SSH_HOST=$SCF4_QA_SSH_HOST; fi
if [ ${BITBUCKET_BRANCH} == master ]; then VERSION_TYPE=$MASTER_VERSION; VERSION_SUFFIX=''; SSH_USER=$SCF4_PROD_SSH_USER; SSH_HOST=$SCF4_PROD_SSH_HOST; fi
if [ ${BITBUCKET_BRANCH} == development ]; then VERSION_TYPE=$DEV_VERSION; VERSION_SUFFIX='-DEV'; SSH_USER=$SCF4_DEV_SSH_USER; SSH_HOST=$SCF4_DEV_SSH_HOST; fi

MVN_VERSION=$(mvn help:evaluate -DskipTests=true -Dexpression=project.version -q -Denv.suffix=${VERSION_SUFFIX} -DforceStdout)
echo $MVN_VERSION
echo $BITBUCKET_COMMIT_SHORT

echo "Starting deployment process..."
echo "Downloading latest build"
curl -o activity-logger.jar ${MAVEN_HTTP_URL}/user-activity-logger/${MVN_VERSION}/user-activity-logger-${MVN_VERSION}.jar.$BITBUCKET_COMMIT_SHORT
echo "Stopping ual service..."
ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "sudo systemctl stop activity-logger.service"
echo "Renaming existing ual.jar file..."
ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "mv ${JAR_HOME_DIR}${JAR_FILE_LOCATION} ${JAR_HOME_DIR}${JAR_FILE_LOCATION}_$(date +%Y%m%d_%H%M%S)"
echo "Copying new ual.jar file to server..."
scp -o StrictHostKeyChecking=no activity-logger.jar $SSH_USER@$SSH_HOST:${JAR_HOME_DIR}${JAR_FILE_LOCATION}
echo "Starting ual service..."
ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "sudo systemctl start activity-logger.service"
echo "Deleting local file"
rm -rf activity-logger.jar