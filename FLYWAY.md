# Flyway Database Migration Guide

This project uses Flyway for database schema migration management. Flyway helps track, manage, and apply database changes in a systematic way.

## Overview

Flyway manages database migrations through versioned SQL scripts. Each script is executed only once, and Flyway keeps track of which migrations have been applied in a special table called `flyway_schema_history`.

## Migration Files

Migration files are located in `src/main/resources/db/migration` and follow this naming convention:

```
V{version}__{description}.sql
```

For example:
- `V1__Initial_Schema.sql`

## Current Migrations

### V1__Initial_Schema.sql
- Creates the initial database schema
- Defines `user_activity_sessions` and `user_activity_events` tables
- Sets up foreign key relationships
- Creates basic indexes for performance

## Configuration

Flyway is configured in `application.properties`:

```properties
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration
spring.flyway.table=flyway_schema_history
spring.flyway.baseline-version=0
spring.flyway.validate-on-migrate=true
spring.flyway.repair-on-migrate=true
spring.flyway.out-of-order=true
```

## Important Settings

- `spring.jpa.hibernate.ddl-auto=validate`: Hibernate will validate the schema but not modify it
- `spring.flyway.baseline-on-migrate=true`: Allows Flyway to work with existing databases
- `spring.flyway.validate-on-migrate=true`: Validates migrations before applying them
- `spring.flyway.repair-on-migrate=true`: Automatically repairs the Flyway schema history table if issues are detected
- `spring.flyway.out-of-order=true`: Allows migrations to be run out of order, useful in team environments

## Adding New Migrations

To add a new database change:

1. Create a new SQL file in `src/main/resources/db/migration`
2. Name it with the next version number (e.g., `V3__Add_New_Feature.sql`)
3. Write the SQL statements for your changes
4. Run the application - Flyway will automatically apply the new migration

## Best Practices

1. **Never modify existing migration files** after they've been committed
2. Each migration should be idempotent when possible (use `IF NOT EXISTS`, etc.)
3. Keep migrations small and focused on specific changes
4. Test migrations thoroughly before deploying to production
5. Include both "up" (apply) and "down" (rollback) logic when possible

## Troubleshooting

If you encounter issues with Flyway migrations:

1. Check the `flyway_schema_history` table to see which migrations have been applied
2. Set `spring.flyway.validate-on-migrate=false` temporarily if you need to debug
3. Use `spring.flyway.repair=true` to repair the metadata table if it becomes corrupted

## Manual Execution

You can also run Flyway migrations manually using Maven:

```bash
mvn flyway:migrate
mvn flyway:info    # Shows migration status
mvn flyway:repair  # Repairs metadata table
```
