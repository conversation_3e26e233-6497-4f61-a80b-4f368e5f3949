package com.veefin.ual.specification;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;

import com.veefin.ual.dto.SessionSearchRequestDTO;
import com.veefin.ual.entity.UserActivitySession;

import jakarta.persistence.criteria.Predicate;

public class UserActivitySessionPaginationSpecification {

	public static Specification<UserActivitySession> fromDTO(SessionSearchRequestDTO dto) {
		return (root, query, cb) -> {
			List<Predicate> andPredicates = new ArrayList<>();

			// Specific filters
			if (dto.getId() != null)
				andPredicates.add(cb.equal(root.get("id"), dto.getId()));
			if (dto.getSystemName() != null)
				andPredicates.add(cb.equal(root.get("systemName"), dto.getSystemName()));
			if (dto.getName() != null)
				andPredicates.add(cb.like(cb.lower(root.get("name")), "%" + dto.getName().toLowerCase() + "%"));
			if (dto.getEmail() != null)
				andPredicates.add(cb.like(cb.lower(root.get("email")), "%" + dto.getEmail().toLowerCase() + "%"));
			if (dto.getUserUuid() != null)
				andPredicates.add(cb.equal(root.get("userUuid"), dto.getUserUuid()));
			if (dto.getClientIp() != null)
				andPredicates.add(cb.like(cb.lower(root.get("clientIp")), "%" + dto.getClientIp().toLowerCase() + "%"));
			if (dto.getUserAgent() != null)
				andPredicates
						.add(cb.like(cb.lower(root.get("userAgent")), "%" + dto.getUserAgent().toLowerCase() + "%"));
			if (dto.getFromDate() != null && dto.getToDate() != null)
				andPredicates.add(cb.between(root.get("createdAt"), dto.getFromDate(), dto.getToDate()));

			// OR-based fulltext search
			if (dto.getSearch() != null && !dto.getSearch().isBlank()) {
				String keyword = "%" + dto.getSearch().toLowerCase() + "%";
				Predicate orPredicate = cb.or(
						cb.like(cb.lower(root.get("id")), keyword),
						cb.like(cb.lower(root.get("systemName")), keyword),
						cb.like(cb.lower(root.get("email")), keyword), 
						cb.like(cb.lower(root.get("name")), keyword),
						cb.like(cb.lower(root.get("userUuid")), keyword),
						cb.like(cb.lower(root.get("clientIp")), keyword),
						cb.like(cb.lower(root.get("userAgent")), keyword),
						cb.like(cb.lower(root.get("source")), keyword));
				andPredicates.add(orPredicate);
			}

			if (dto.getFromDate() != null)
				andPredicates.add(cb.greaterThanOrEqualTo(root.get("createdAt"), dto.getFromDate()));

			if (dto.getToDate() != null)
				andPredicates.add(cb.lessThanOrEqualTo(root.get("createdAt"), dto.getToDate()));

			return cb.and(andPredicates.toArray(new Predicate[0]));
		};
	}
}
