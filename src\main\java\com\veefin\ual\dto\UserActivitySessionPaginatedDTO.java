package com.veefin.ual.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class UserActivitySessionPaginatedDTO {

	@JsonProperty("total_count")
	private long totalCount;

	@JsonProperty("sessions")
	private List<UserActivitySessionResponseDTO> sessions;

	public UserActivitySessionPaginatedDTO() {
	}

	public UserActivitySessionPaginatedDTO(long totalCount, List<UserActivitySessionResponseDTO> sessions) {
		this.totalCount = totalCount;
		this.sessions = sessions;
	}

	public long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}

	public List<UserActivitySessionResponseDTO> getSessions() {
		return sessions;
	}

	public void setSessions(List<UserActivitySessionResponseDTO> sessions) {
		this.sessions = sessions;
	}

}
