package com.veefin.ual.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class UserActivityEventsDTO {
	@NotBlank(message = "Module is required")
	private String module;

	@NotBlank(message = "Action is Required")
	private String action;

	@JsonProperty("user_agent")
	@JsonAlias({ "userAgent", "user_agent" })
	private String userAgent;

	@JsonProperty("client_ip")
	@JsonAlias({ "clientIp", "client_ip" })
	private String clientIp;

	@NotNull(message = "user_activity_session_id is required")
	@JsonProperty("user_activity_session_id")
	@JsonAlias({ "userActivitySessionId", "user_activity_session_id" })
	private String userActivitySessionId;

	@NotNull(message = "system_name is required")
	@JsonProperty("system_name")
	@JsonAlias({ "systemName", "system_name" })
	private String systemName;

	@JsonProperty("event_at")
	@JsonAlias({ "eventAt", "event_at" })
	private LocalDateTime eventAt = LocalDateTime.now();

	private JsonNode properties;

	private String message;

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getUserActivitySessionId() {
		return userActivitySessionId;
	}

	public void setUserActivitySessionId(String userActivitySessionId) {
		this.userActivitySessionId = userActivitySessionId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public LocalDateTime getEventAt() {
		return eventAt;
	}

	public void setEventAt(LocalDateTime eventAt) {
		this.eventAt = eventAt;
	}



	public String getMessage() {
		return message;
	}

	public JsonNode getProperties() {
		return properties;
	}

	public void setProperties(JsonNode properties) {
		this.properties = properties;
	}

	public void setMessage(String message) {
		this.message = message;
	}

}
