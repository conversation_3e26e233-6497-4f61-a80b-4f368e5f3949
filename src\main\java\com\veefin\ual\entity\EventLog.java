package com.veefin.ual.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "user_activity_events")
public class EventLog extends BaseEntity {

	@Column(nullable = false)
	private String module;

	@Column(nullable = false)
	private String action;

	@Column(name = "user_activity_session_id", nullable = false)
	private String userActivitySessionId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_activity_session_id", referencedColumnName = "id", insertable = false, updatable = false)
	private UserActivitySession userActivitySession;

	@Column(nullable = false)
	private String systemName;

	@Column(columnDefinition = "JSON")
	private String properties;

	private String clientIp;

	private String userAgent;

	@Column(nullable = false)
	private LocalDateTime eventAt;

	@Column(name = "message")
	private String message;

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getUserActivitySessionId() {
		return userActivitySessionId;
	}

	public void setUserActivitySessionId(String userActivitySessionId) {
		this.userActivitySessionId = userActivitySessionId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public LocalDateTime getEventAt() {
		return eventAt;
	}

	public void setEventAt(LocalDateTime eventAt) {
		this.eventAt = eventAt;
	}

	public String getProperties() {
		return properties;
	}

	public void setProperties(String properties) {
		this.properties = properties;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public UserActivitySession getUserActivitySession() {
		return userActivitySession;
	}

	public void setUserActivitySession(UserActivitySession userActivitySession) {
		this.userActivitySession = userActivitySession;
		if (userActivitySession != null) {
			this.userActivitySessionId = userActivitySession.getId();
		}
	}
}
