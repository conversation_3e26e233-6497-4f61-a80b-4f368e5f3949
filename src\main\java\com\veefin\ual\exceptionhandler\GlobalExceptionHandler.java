package com.veefin.ual.exceptionhandler;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.coyote.BadRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.service.SentryService;
import com.veefin.ual.utilities.ErrorCode;
import com.veefin.ual.utilities.MessageService;

@RestControllerAdvice
public class GlobalExceptionHandler {
	private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

	private final MessageService messageService;
	private final SentryService sentryService;

	public GlobalExceptionHandler(MessageService messageService, SentryService sentryService) {
		this.messageService = messageService;
		this.sentryService = sentryService;
	}

	@ExceptionHandler(VeefinException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseDTO<?> handleVeefinException(VeefinException ex) {
		sentryService.captureException(ex);
		String message = messageService.getMessage(ex.getErrorCode().getMessage(), ex.getMessageArgs());

		log.error("Exception: {}, Message: {}", ex.getErrorCode(), message);

		return new ResponseDTO<>(ex.getErrorCode().getCode(), message, null);
	}

	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResponseEntity<ResponseDTO<?>> handleValidationExceptions(MethodArgumentNotValidException ex) {
		sentryService.captureException(ex);

		Map<String, String> errors = new HashMap<>();
		ex.getBindingResult().getFieldErrors().forEach(error -> errors.put(error.getField(), error.getDefaultMessage()) // Extract
																														// field
																														// and
																														// message
		);
		ResponseDTO<?> responseDTO = new ResponseDTO<>(ErrorCode.VALIDATION_ERROR.getCode(),
				errors.values().stream().collect(Collectors.joining(",")), errors);
		return new ResponseEntity<>(responseDTO, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler({ BadRequestException.class })
	public ResponseEntity<ResponseDTO<?>> handleCustomExceptionForInvalidInput(
			BadRequestException badRequestException) {
		sentryService.captureException(badRequestException);

		ResponseDTO<?> status = new ResponseDTO<>(ErrorCode.BAD_REQUEST.getCode(), badRequestException.getMessage(),
				null);
		return new ResponseEntity<>(status, HttpStatusCode.valueOf(HttpStatus.CONFLICT.value()));
	}

	@ExceptionHandler({ MissingServletRequestParameterException.class })
	public ResponseEntity<ResponseDTO<?>> handleMissingServletRequestParameterException(
			MissingServletRequestParameterException exception) {
		sentryService.captureException(exception);

		ResponseDTO responseDTO = new ResponseDTO(ErrorCode.BAD_REQUEST.getCode(),
				"Required parameter %s is not present", null);
		return new ResponseEntity<>(responseDTO, HttpStatusCode.valueOf(HttpStatus.BAD_REQUEST.value()));
	}

}
