package com.veefin.ual.repository;

import com.veefin.ual.config.TestConfig;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.entity.UserActivitySession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@ActiveProfiles("test")
@Import(TestConfig.class)
public class UserActivitySessionRepositoryTest {

    @Autowired
    private UserActivitySessionRepository userActivitySessionRepository;

    private UserActivitySession userActivitySession;
    private LocalDateTime startDate;
    private LocalDateTime endDate;

    @BeforeEach
    void setUp() {
        // Initialize test data
        startDate = LocalDateTime.now().minusDays(7);
        endDate = LocalDateTime.now().plusDays(1);

        userActivitySession = new UserActivitySession();
        userActivitySession.setId(UUID.randomUUID().toString());
        userActivitySession.setEmail("<EMAIL>");
        userActivitySession.setName("Test User");
        userActivitySession.setUserUuid("test-uuid");
        userActivitySession.setSystemName("TEST");
        userActivitySession.setUserAgent("Test Agent");
        userActivitySession.setClientIp("127.0.0.1");
        userActivitySession.setSource("WEB");
        userActivitySession.setCreatedAt(LocalDateTime.now());

        // Save the test entity
        userActivitySessionRepository.save(userActivitySession);
    }

    @Test
    void testFilteredUserSession() {
        // Create pageable
        Pageable pageable = PageRequest.of(0, 10);

        // Call the repository method
        Page<UserActivitySessionDTO> result = userActivitySessionRepository.filteredUserSession("Test", startDate, endDate, pageable);

        // Verify the result
        assertNotNull(result);
        assertTrue(result.hasContent());
        assertEquals(1, result.getTotalElements());

        UserActivitySessionDTO dto = result.getContent().get(0);
        assertEquals("Test User", dto.getName());
        assertEquals("<EMAIL>", dto.getEmail());
        assertEquals("test-uuid", dto.getUuid());
        assertEquals("TEST", dto.getSystemName());
    }

    @Test
    void testFilteredUserSessionWithNoMatch() {
        // Create pageable
        Pageable pageable = PageRequest.of(0, 10);

        // Call the repository method with a search term that won't match
        Page<UserActivitySessionDTO> result = userActivitySessionRepository.filteredUserSession("NonExistent", startDate, endDate, pageable);

        // Verify the result
        assertNotNull(result);
        assertFalse(result.hasContent());
        assertEquals(0, result.getTotalElements());
    }
}
