# User Activity Logger Documentation

## Overview

The User Activity Logger (UAL) is a Spring Boot application designed to track and record user activities across various systems. It provides a centralized logging mechanism for user sessions and events, allowing for comprehensive activity monitoring and analysis.

## Architecture

The application follows a layered architecture pattern:

```
┌─────────────────────────────────────────────────────────────┐
│                        Controllers                          │
│  (Handle HTTP requests and delegate to appropriate services) │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                         Services                            │
│     (Implement business logic and transaction handling)     │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      Repositories                           │
│        (Handle data access and persistence operations)      │
└───────────────────────────────┬─────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      Database/Queue                         │
│     (PostgreSQL for storage, RabbitMQ for async events)     │
└─────────────────────────────────────────────────────────────┘
```

## Flow Diagram

The following diagram illustrates the flow of data through the system:

```
┌──────────────┐     ┌───────────────────┐     ┌───────────────────┐
│              │     │                   │     │                   │
│  Client App  ├────►│  Session Creation ├────►│  Session Storage  │
│              │     │                   │     │                   │
└──────────────┘     └───────────────────┘     └─────────┬─────────┘
                                                         │
                                                         ▼
┌──────────────┐     ┌───────────────────┐     ┌───────────────────┐
│              │     │                   │     │                   │
│  Event Query │◄────┤  Event Retrieval  │◄────┤   Event Storage   │
│              │     │                   │     │                   │
└──────────────┘     └───────────────────┘     └─────────▲─────────┘
                                                         │
                                                         │
┌──────────────┐     ┌───────────────────┐     ┌─────────┴─────────┐
│              │     │                   │     │                   │
│  Client App  ├────►│   Event Logging   ├────►│  RabbitMQ Queue   │
│              │     │                   │     │                   │
└──────────────┘     └───────────────────┘     └───────────────────┘
```

## Key Components

### Controllers

1. **UserActivityLogController**: Handles HTTP requests for session creation, event logging, and data retrieval.

### Services

1. **UserActivitySessionService**: Manages user sessions and processes activity events.
2. **EventSearchService**: Provides functionality to search and retrieve logged events.

### Repositories

1. **UserActivitySessionRepository**: Handles persistence operations for user sessions.
2. **EventLogRepository**: Manages storage and retrieval of event logs.

### DTOs (Data Transfer Objects)

1. **UserActivitySessionDTO**: Represents user session data.
2. **UserActivityEventsDTO**: Contains event data for logging.
3. **EventSearchRequestDTO**: Used for searching events.
4. **SessionSearchRequestDTO**: Used for searching sessions.
5. **ResponseDTO**: Generic response wrapper.

### Entities

1. **UserActivitySession**: Represents a user session in the database.
2. **EventLog**: Represents a logged event in the database.

## API Endpoints

### Session Management

- **POST /v1/identify**: Creates a new user activity session
  - Request: `UserActivitySessionDTO`
  - Response: Session ID

### Event Logging

- **POST /v1/events**: Logs user activity events
  - Request: `UserActivityEventsDTO`
  - Response: Confirmation of event processing

### Data Retrieval

- **POST /v1/events/search**: Searches for events based on criteria
  - Request: `EventSearchRequestDTO`
  - Response: Matching events

- **POST /v1/user-activity-sessions/search**: Searches for user sessions
  - Request: `SessionSearchRequestDTO`
  - Response: Matching sessions

## Asynchronous Processing

The system uses RabbitMQ for asynchronous event processing:

1. Events are received via the API
2. Events are published to a RabbitMQ queue
3. Events are consumed and persisted to the database
4. If RabbitMQ is unavailable, events are directly persisted to the database

## Configuration

The application uses Spring profiles for different environments:

- **dev**: Development environment configuration
- **test**: Test environment configuration
- **prod**: Production environment configuration

## Database Schema

### UserActivitySession Table

| Column       | Type         | Description                       |
|--------------|--------------|-----------------------------------|
| id           | VARCHAR(255) | Primary key (UUID)                |
| name         | VARCHAR(255) | User's name                       |
| email        | VARCHAR(255) | User's email                      |
| user_uuid    | VARCHAR(255) | User's UUID from source system    |
| system_name  | VARCHAR(255) | Source system name                |
| user_agent   | TEXT         | User's browser/client information |
| client_ip    | VARCHAR(255) | User's IP address                 |
| source       | VARCHAR(255) | Source of the session (Web/Mobile)|
| created_at   | TIMESTAMP    | Session creation timestamp        |

### EventLog Table

| Column                  | Type         | Description                       |
|-------------------------|--------------|-----------------------------------|
| id                      | VARCHAR(255) | Primary key (UUID)                |
| user_activity_session_id| VARCHAR(255) | Foreign key to UserActivitySession|
| module                  | VARCHAR(255) | Module where event occurred       |
| action                  | VARCHAR(255) | Action performed                  |
| system_name             | VARCHAR(255) | System where event occurred       |
| properties              | TEXT         | JSON properties of the event      |
| created_at              | TIMESTAMP    | Event creation timestamp          |
| event_at                | TIMESTAMP    | When the event occurred           |

## Security Considerations

- The application uses Spring Security for authentication and authorization
- API endpoints are secured with appropriate access controls
- Sensitive data is properly sanitized and validated

## Monitoring and Health Checks

- Spring Actuator endpoints are enabled for monitoring
- Custom health indicators for RabbitMQ connection status
- JaCoCo is configured for code coverage analysis

## Deployment

The application can be deployed as a standalone JAR or as a Docker container. Environment-specific configurations are managed through Spring profiles and environment variables.
