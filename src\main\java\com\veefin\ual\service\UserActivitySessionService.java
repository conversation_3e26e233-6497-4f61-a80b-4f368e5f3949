package com.veefin.ual.service;


import java.util.concurrent.CompletableFuture;

import com.veefin.ual.dto.PaginationRequestDTO;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.SessionSearchRequestDTO;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.exceptionhandler.ResourceNotFound;

public interface UserActivitySessionService {
	ResponseDTO<?> addUserActivitySession(UserActivitySessionDTO userActivitySessionDTO) throws ResourceNotFound;

	ResponseDTO<?> addUserEventsDbOrQueue(UserActivityEventsDTO userActivityEventsDTO);

	ResponseDTO<?> getUserSessionWithFilters(PaginationRequestDTO paginationRequestDTO);

	ResponseDTO<?> getUserEventsWithFilters(PaginationRequestDTO paginationRequestDTO, String sessionId);

	ResponseDTO<?> getEvents(PaginationRequestDTO paginationRequestDTO);

	ResponseDTO<?> getUserSessions(SessionSearchRequestDTO requestDto);

	/**
	 * Example method showing how to use CompletableFuture with virtual threads.
	 * This method demonstrates how to run a task asynchronously using virtual threads.
	 *
	 * @param userActivityEventsDTO The DTO containing user activity events
	 * @return A CompletableFuture that will complete with the result of processing the events
	 */
	CompletableFuture<ResponseDTO<?>> processEventsAsync(UserActivityEventsDTO userActivityEventsDTO);

	/**
	 * Tests the RabbitMQ connection by sending a test message
	 * @return Response with success or error message
	 */
	//ResponseDTO<?> testRabbitMQConnection();
}
