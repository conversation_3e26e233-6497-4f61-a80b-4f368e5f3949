package com.veefin.ual.utilities;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("test")
public class UserActivitySessionUtilTest {

    private UserActivitySessionUtil userActivitySessionUtil;
    private UserActivitySessionDTO userActivitySessionDTO;
    private UserActivityEventsDTO userActivityEventsDTO;
    private String sessionId;

    @BeforeEach
    void setUp() {
        userActivitySessionUtil = new UserActivitySessionUtil();
        sessionId = UUID.randomUUID().toString();

        userActivitySessionDTO = new UserActivitySessionDTO();
        userActivitySessionDTO.setEmail("<EMAIL>");
        userActivitySessionDTO.setName("Test User");
        userActivitySessionDTO.setUuid("test-uuid");
        userActivitySessionDTO.setSystemName("TEST");
        userActivitySessionDTO.setUserAgent("Test Agent");
        userActivitySessionDTO.setClientIp("127.0.0.1");
        userActivitySessionDTO.setSource("WEB");

        userActivityEventsDTO = new UserActivityEventsDTO();
        userActivityEventsDTO.setUserActivitySessionId(sessionId);
        userActivityEventsDTO.setModule("TEST_MODULE");
        userActivityEventsDTO.setAction("TEST_ACTION");
        userActivityEventsDTO.setSystemName("TEST");

        // Create a simple JsonNode for properties
        ObjectMapper mapper = new ObjectMapper();
        JsonNode propertiesNode = mapper.createObjectNode();
        ((com.fasterxml.jackson.databind.node.ObjectNode) propertiesNode).put("test", "value");
        userActivityEventsDTO.setProperties(propertiesNode);
    }

    @Test
    void testConvertDtoToEntity() {
        // Call the utility method
        UserActivitySession entity = userActivitySessionUtil.convertDtoTOUserActivitySession(userActivitySessionDTO);

        // Verify the result
        assertNotNull(entity);
        assertEquals(userActivitySessionDTO.getName(), entity.getName());
        assertEquals(userActivitySessionDTO.getEmail(), entity.getEmail());
        assertEquals(userActivitySessionDTO.getUuid(), entity.getUserUuid());
        assertEquals(userActivitySessionDTO.getSystemName(), entity.getSystemName());
        assertEquals(userActivitySessionDTO.getUserAgent(), entity.getUserAgent());
        assertEquals(userActivitySessionDTO.getClientIp(), entity.getClientIp());
        assertEquals(userActivitySessionDTO.getSource(), entity.getSource());
    }

    @Test
    void testConvertDtoToEventLog() {
        // Call the utility method
        EventLog eventLog = userActivitySessionUtil.convertDtoToEventLog(userActivityEventsDTO);

        // Verify the result
        assertNotNull(eventLog);
        assertEquals(userActivityEventsDTO.getUserActivitySessionId(), eventLog.getUserActivitySessionId());
        assertEquals(userActivityEventsDTO.getModule(), eventLog.getModule());
        assertEquals(userActivityEventsDTO.getAction(), eventLog.getAction());
        assertEquals(userActivityEventsDTO.getSystemName(), eventLog.getSystemName());
        assertEquals("{\"test\":\"value\"}", eventLog.getProperties());
    }

    @Test
    void testDeserializeJsonWithEventAtCamelCase() throws Exception {
        // Create a JSON string with eventAt in camelCase format
        String jsonWithCamelCase = "{\n" +
                "  \"module\": \"TEST_MODULE\",\n" +
                "  \"action\": \"TEST_ACTION\",\n" +
                "  \"user_activity_session_id\": \"" + sessionId + "\",\n" +
                "  \"system_name\": \"TEST\",\n" +
                "  \"eventAt\": \"2023-05-01T10:15:30\"\n" +
                "}";

        // Create ObjectMapper with JavaTimeModule for LocalDateTime support
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());

        // Deserialize the JSON
        UserActivityEventsDTO dto = mapper.readValue(jsonWithCamelCase, UserActivityEventsDTO.class);

        // Verify the result
        assertNotNull(dto);
        assertEquals("TEST_MODULE", dto.getModule());
        assertEquals("TEST_ACTION", dto.getAction());
        assertEquals(sessionId, dto.getUserActivitySessionId());
        assertEquals("TEST", dto.getSystemName());
        assertNotNull(dto.getEventAt());
        assertEquals(LocalDateTime.parse("2023-05-01T10:15:30"), dto.getEventAt());
    }

    @Test
    void testDeserializeJsonWithEventAtSnakeCase() throws Exception {
        // Create a JSON string with event_at in snake_case format
        String jsonWithSnakeCase = "{\n" +
                "  \"module\": \"TEST_MODULE\",\n" +
                "  \"action\": \"TEST_ACTION\",\n" +
                "  \"user_activity_session_id\": \"" + sessionId + "\",\n" +
                "  \"system_name\": \"TEST\",\n" +
                "  \"event_at\": \"2023-05-01T10:15:30\"\n" +
                "}";

        // Create ObjectMapper with JavaTimeModule for LocalDateTime support
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());

        // Deserialize the JSON
        UserActivityEventsDTO dto = mapper.readValue(jsonWithSnakeCase, UserActivityEventsDTO.class);

        // Verify the result
        assertNotNull(dto);
        assertEquals("TEST_MODULE", dto.getModule());
        assertEquals("TEST_ACTION", dto.getAction());
        assertEquals(sessionId, dto.getUserActivitySessionId());
        assertEquals("TEST", dto.getSystemName());
        assertNotNull(dto.getEventAt());
        assertEquals(LocalDateTime.parse("2023-05-01T10:15:30"), dto.getEventAt());
    }
}
