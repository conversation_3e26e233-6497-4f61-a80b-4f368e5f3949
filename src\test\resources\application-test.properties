spring.application.name=ual
server.servlet.context-path=/ual
server.port=0

# H2 Database Configuration for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# Disable Flyway for tests
spring.flyway.enabled=false

# RabbitMQ configuration (mocked in tests)
rabbitmq.queue.name=test-event-message-queue
rabbitmq.exchange.name=test-event-message-exchange
rabbitmq.routing.key=test_event_message
add.event.db=true

# Redis configuration (mocked in tests)
spring.redis.host=localhost
spring.redis.port=6379
SECURITY_REDIS_HOST=localhost
SECURITY_REDIS_PORT=6379

# JWT Authentication Keys (mocked in tests)
jwt.rsa-public-key=test-key
oauth.server.issuer=test-oauth

# Disable Sentry in tests
sentry.enabled=false
sentry.dsn=
sentry.traces-sample-rate=0.0
sentry.attach-stacktrace=false

# Logging configuration
logging.level.root=INFO
logging.level.com.veefin.ual=DEBUG
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO

# Enable virtual threads for Spring Boot
spring.threads.virtual.enabled=true
