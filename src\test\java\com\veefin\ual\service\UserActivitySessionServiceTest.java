package com.veefin.ual.service;

import com.veefin.ual.config.TestConfig;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.SessionSearchRequestDTO;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import com.veefin.ual.exceptionhandler.ResourceNotFound;
import com.veefin.ual.repository.EventLogRepository;
import com.veefin.ual.repository.UserActivitySessionRepository;
import com.veefin.ual.service.impl.UserActivitySessionServiceImpl;
import com.veefin.ual.utilities.UserActivitySessionUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.*;

import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@ActiveProfiles("test")
public class UserActivitySessionServiceTest {

    @Mock
    private UserActivitySessionRepository userActivitySessionRepository;

    @Mock
    private EventLogRepository eventLogRepository;

    @Mock
    private RabbitTemplate rabbitTemplate;

    @Mock
    private UserActivitySessionUtil userActivitySessionUtil;

    @InjectMocks
    private UserActivitySessionServiceImpl userActivitySessionService;

    private UserActivitySessionDTO userActivitySessionDTO;
    private UserActivityEventsDTO userActivityEventsDTO;
    private UserActivitySession userActivitySession;
    private EventLog eventLog;
    private String sessionId;

    @BeforeEach
    void setUp() {
        // Initialize test data
        sessionId = UUID.randomUUID().toString();

        userActivitySessionDTO = new UserActivitySessionDTO();
        userActivitySessionDTO.setEmail("<EMAIL>");
        userActivitySessionDTO.setName("Test User");
        userActivitySessionDTO.setUuid("test-uuid");
        userActivitySessionDTO.setSystemName("TEST");
        userActivitySessionDTO.setUserAgent("Test Agent");
        userActivitySessionDTO.setClientIp("127.0.0.1");
        userActivitySessionDTO.setSource("WEB");

        userActivitySession = new UserActivitySession();
        userActivitySession.setId(sessionId);
        userActivitySession.setEmail("<EMAIL>");
        userActivitySession.setName("Test User");
        userActivitySession.setUserUuid("test-uuid");
        userActivitySession.setSystemName("TEST");
        userActivitySession.setUserAgent("Test Agent");
        userActivitySession.setClientIp("127.0.0.1");
        userActivitySession.setSource("WEB");
        userActivitySession.setCreatedAt(LocalDateTime.now());

        userActivityEventsDTO = new UserActivityEventsDTO();
        userActivityEventsDTO.setUserActivitySessionId(sessionId);
        userActivityEventsDTO.setModule("TEST_MODULE");
        userActivityEventsDTO.setAction("TEST_ACTION");
        userActivityEventsDTO.setSystemName("TEST");

        eventLog = new EventLog();
        eventLog.setId(UUID.randomUUID().toString());
        eventLog.setUserActivitySessionId(sessionId);
        eventLog.setModule("TEST_MODULE");
        eventLog.setAction("TEST_ACTION");
        eventLog.setSystemName("TEST");
        eventLog.setCreatedAt(LocalDateTime.now());
        eventLog.setEventAt(LocalDateTime.now());
    }

    @Test
    void testAddUserActivitySession() throws ResourceNotFound {
        // Mock repository behavior
        when(userActivitySessionUtil.convertDtoTOUserActivitySession(any(UserActivitySessionDTO.class))).thenReturn(userActivitySession);
        when(userActivitySessionRepository.save(any(UserActivitySession.class))).thenReturn(userActivitySession);

        // Call the service method
        ResponseDTO<?> responseDTO = userActivitySessionService.addUserActivitySession(userActivitySessionDTO);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals(sessionId, responseDTO.getData());

        // Verify repository was called
        verify(userActivitySessionRepository, times(1)).save(any(UserActivitySession.class));
    }

    @Test
    void testAddUserEventsDbOrQueue() {
        // Mock repository behavior
        when(userActivitySessionUtil.convertDtoToEventLog(any(UserActivityEventsDTO.class))).thenReturn(eventLog);
        when(eventLogRepository.save(any(EventLog.class))).thenReturn(eventLog);

        // Use lenient stubbing for RabbitMQ
        lenient().doNothing().when(rabbitTemplate).convertAndSend(isNull(), isNull(), any(UserActivityEventsDTO.class));

        // Call the service method
        ResponseDTO<?> responseDTO = userActivitySessionService.addUserEventsDbOrQueue(userActivityEventsDTO);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());

        // Since RabbitMQ is working in the test, we don't expect DB save to be called
        // Just verify the response is correct
    }

    @Test
    void testProcessEventsAsync() throws Exception {
        // Mock repository behavior
        when(userActivitySessionUtil.convertDtoToEventLog(any(UserActivityEventsDTO.class))).thenReturn(eventLog);
        when(eventLogRepository.save(any(EventLog.class))).thenReturn(eventLog);

        // Use lenient stubbing for RabbitMQ
        lenient().doNothing().when(rabbitTemplate).convertAndSend(isNull(), isNull(), any(UserActivityEventsDTO.class));

        // Call the service method
        CompletableFuture<ResponseDTO<?>> futureResponse = userActivitySessionService.processEventsAsync(userActivityEventsDTO);

        // Wait for the future to complete
        ResponseDTO<?> responseDTO = futureResponse.get();

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());

        // Since RabbitMQ is working in the test, we don't expect DB save to be called
        // Just verify the response is correct
    }

    @Test
    void testGetUserSessions() {
        // Create test data
        SessionSearchRequestDTO requestDto = new SessionSearchRequestDTO();
        requestDto.setPage(0);
        requestDto.setSize(10);
        requestDto.setSystemName("TEST");

        List<UserActivitySession> sessionList = new ArrayList<>();
        sessionList.add(userActivitySession);
        Page<UserActivitySession> page = new PageImpl<>(sessionList);

        // Mock repository behavior
        when(userActivitySessionRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        // Call the service method
        ResponseDTO<?> responseDTO = userActivitySessionService.getUserSessions(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("Pagination for sessions run successfully", responseDTO.getMessage());

        // Verify repository was called
        verify(userActivitySessionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testGetUserSessionsWithEmptyResults() {
        // Create test data
        SessionSearchRequestDTO requestDto = new SessionSearchRequestDTO();
        requestDto.setPage(0);
        requestDto.setSize(10);
        requestDto.setSystemName("TEST");

        // Mock repository behavior
        when(userActivitySessionRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(Page.empty());

        // Call the service method
        ResponseDTO<?> responseDTO = userActivitySessionService.getUserSessions(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("No sessions found", responseDTO.getMessage());

        // Verify repository was called
        verify(userActivitySessionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testGetUserSessionsWithNullPage() {
        // Create test data
        SessionSearchRequestDTO requestDto = new SessionSearchRequestDTO();
        requestDto.setPage(0);
        requestDto.setSize(10);
        requestDto.setSystemName("TEST");

        // Mock repository behavior to return null
        when(userActivitySessionRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(null);

        // Call the service method
        ResponseDTO<?> responseDTO = userActivitySessionService.getUserSessions(requestDto);

        // Verify the result
        assertNotNull(responseDTO);
        assertEquals(HttpStatus.OK.toString(), responseDTO.getCode());
        assertEquals("No sessions found", responseDTO.getMessage());

        // Verify repository was called
        verify(userActivitySessionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
    }
}
