package com.veefin.ual.actuator;

import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Custom health indicator that checks RabbitMQ connectivity
 * Optimized to minimize connection creation
 */
@Component("customRabbitMQHealthIndicator")
public class RabbitMQHealthIndicator implements HealthIndicator {

    private final RabbitTemplate rabbitTemplate;
    private volatile long lastCheckTime = 0;
    private final AtomicReference<Health> lastHealth = new AtomicReference<>(Health.unknown().build());
    private static final long CHECK_INTERVAL = 60000; // 60 seconds between actual checks

    public RabbitMQHealthIndicator(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    @Override
    public Health health() {
        // Return cached health status if it's recent enough
        long now = System.currentTimeMillis();
        if (now - lastCheckTime < CHECK_INTERVAL) {
            return lastHealth.get();
        }

        try {
            // Get the connection factory
            ConnectionFactory connectionFactory = rabbitTemplate.getConnectionFactory();

            // Check if there's an existing connection we can use
            Connection connection = null;
            boolean connectionEstablished = false;

            try {
                // Try to get an existing connection from the factory's cache
                // This avoids creating a new connection if possible
                connection = connectionFactory.createConnection();
                connectionEstablished = connection.isOpen();
            } finally {
                // Make sure to close any connection we created
                if (connection != null) {
                    connection.close();
                }
            }

            Health health;
            if (connectionEstablished) {
                health = Health.up()
                        .withDetail("service", "RabbitMQ")
                        .withDetail("status", "Connected")
                        .withDetail("timestamp", now)
                        .build();
            } else {
                health = Health.down()
                        .withDetail("service", "RabbitMQ")
                        .withDetail("status", "Disconnected")
                        .withDetail("timestamp", now)
                        .build();
            }

            // Cache the result
            lastHealth.set(health);
            lastCheckTime = now;
            return health;
        } catch (Exception e) {
            Health health = Health.down()
                    .withDetail("service", "RabbitMQ")
                    .withDetail("status", "Error")
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", now)
                    .build();

            // Cache the result
            lastHealth.set(health);
            lastCheckTime = now;
            return health;
        }
    }
}
