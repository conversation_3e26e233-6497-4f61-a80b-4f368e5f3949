package com.veefin.ual.repository;

import java.time.LocalDateTime;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;

@Repository
public interface UserActivitySessionRepository
		extends JpaRepository<UserActivitySession, String>, JpaSpecificationExecutor<UserActivitySession> {
	@Query(value = Queries.LISTING_QUERY + Queries.LISTING_WHERE_QUERY, countQuery = Queries.LISTING_COUNT_QUERY)
	Page<UserActivitySessionDTO> filteredUserSession(@Param("search") String search,
			@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, Pageable pageable);

	class Queries {
		public static final String LISTING_QUERY = "SELECT new com.veefin.ual.dto.UserActivitySessionDTO(u.name, u.email, u.userUuid, u.systemName, u.userAgent, u.clientIp, u.source) "
				+ "FROM UserActivitySession u";

		public static final String LISTING_WHERE_QUERY = " WHERE (:search IS NULL OR "
				+ " LOWER(u.name) LIKE LOWER(CONCAT('%', :search, '%')) OR "
				+ " LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%')) OR "
				+ " LOWER(u.userUuid) LIKE LOWER(CONCAT('%', :search, '%')) OR "
				+ " LOWER(u.systemName) LIKE LOWER(CONCAT('%', :search, '%')) OR "
				+ " LOWER(u.userAgent) LIKE LOWER(CONCAT('%', :search, '%')) OR "
				+ " LOWER(u.clientIp) LIKE LOWER(CONCAT('%', :search, '%')) OR "
				+ " LOWER(u.source) LIKE LOWER(CONCAT('%', :search, '%')) " + ") " + " AND " + " ( "
				+ " (:startDate IS NULL OR :endDate IS NULL) " + " OR "
				+ " u.createdAt BETWEEN :startDate AND :endDate " + ")";

		public static final String LISTING_COUNT_QUERY = "SELECT COUNT(u) FROM UserActivitySession u"
				+ LISTING_WHERE_QUERY;
	}

}
