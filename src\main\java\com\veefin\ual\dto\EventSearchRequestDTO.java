package com.veefin.ual.dto;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Request DTO for advanced event search with enhanced filtering options")
public class EventSearchRequestDTO {

	@Schema(description = "Filter events by module name (partial match)")
	private String module;

	@Schema(description = "Filter events by action name (partial match)")
	private String action;

	@JsonProperty("user_activity_session_id")
	@Schema(description = "Filter events by exact session ID")
	private String userActivitySessionId;

	@JsonProperty("user_name")
	@Schema(description = "Filter events by user name (partial match by default)")
	private String userName;

	@JsonProperty("user_email")
	@Schema(description = "Filter events by user email (partial match by default)")
	private String userEmail;

	@JsonProperty("client_ip")
	@Schema(description = "Filter events by client IP address (partial match)")
	private String clientIp;

	@JsonProperty("user_agent")
	@Schema(description = "Filter events by user agent string (partial match)")
	private String userAgent;

	@JsonProperty("system_name")
	@Schema(description = "Filter events by system name (partial match)")
	private String systemName;

	@JsonProperty("search")
	@Schema(description = "Global search query - searches across multiple fields including user name, email, module, action, etc.")
	private String search;

	@Schema(description = "Page number (0-based)", defaultValue = "0")
	private int page = 0;

	@Schema(description = "Page size - number of records per page", defaultValue = "20")
	private int size = 20;

	@JsonProperty("from_date")
	@Schema(description = "Start date for filtering events (inclusive)")
	private LocalDate fromDate;

	@JsonProperty("to_date")
	@Schema(description = "End date for filtering events (inclusive)")
	private LocalDate toDate;

	public LocalDate getFromDate() {
		return fromDate;
	}

	public void setFromDate(LocalDate fromDate) {
		this.fromDate = fromDate;
	}

	public LocalDate getToDate() {
		return toDate;
	}

	public void setToDate(LocalDate toDate) {
		this.toDate = toDate;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getUserActivitySessionId() {
		return userActivitySessionId;
	}

	public void setUserActivitySessionId(String userActivitySessionId) {
		this.userActivitySessionId = userActivitySessionId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getSearch() {
		return search;
	}

	public void setSearch(String search) {
		this.search = search;
	}

	public int getPage() {
		return page;
	}

	public void setPage(int page) {
		this.page = page;
	}

	public int getSize() {
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}

}
