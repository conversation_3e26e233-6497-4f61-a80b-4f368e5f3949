package com.veefin.ual.service.impl;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.veefin.ual.dto.PaginatedResult;
import com.veefin.ual.dto.PaginationRequestDTO;
import com.veefin.ual.dto.ResponseDTO;
import com.veefin.ual.dto.SessionSearchRequestDTO;
import com.veefin.ual.dto.UserActivityEventsDTO;
import com.veefin.ual.dto.UserActivitySessionDTO;
import com.veefin.ual.dto.UserActivitySessionPaginatedDTO;
import com.veefin.ual.dto.UserActivitySessionResponseDTO;
import com.veefin.ual.entity.EventLog;
import com.veefin.ual.entity.UserActivitySession;
import com.veefin.ual.exceptionhandler.ResourceNotFound;
import com.veefin.ual.repository.EventLogRepository;
import com.veefin.ual.repository.UserActivitySessionRepository;
import com.veefin.ual.service.UserActivitySessionService;
import com.veefin.ual.specification.UserActivitySessionPaginationSpecification;
import com.veefin.ual.service.SentryService;
import com.veefin.ual.utilities.EventLogSpecification;
import com.veefin.ual.utilities.UserActivitySessionUtil;

@Service
public class UserActivitySessionServiceImpl implements UserActivitySessionService {

	// Constants for response messages
	private static final String NULL_PAGE_WARNING = "Null page result returned from repository";
	private static final String NO_EVENTS_FOUND = "No events found";
	private static final String NO_SESSIONS_FOUND = "No sessions found";
	private static final String ERROR_FETCHING_SESSIONS = "Error fetching user sessions";
	private static final String ERROR_FETCHING_EVENTS = "Error fetching events";

	private final UserActivitySessionRepository userActivitySessionRepository;

	private final UserActivitySessionUtil userActivitySessionUtil;

	private final RabbitTemplate rabbitTemplate;

	private final EventLogRepository eventLogRepository;

	private final SentryService sentryService;

	public UserActivitySessionServiceImpl(UserActivitySessionRepository userActivitySessionRepository,
			UserActivitySessionUtil userActivitySessionUtil, RabbitTemplate rabbitTemplate,
			EventLogRepository eventLogRepository, SentryService sentryService) {
		this.userActivitySessionRepository = userActivitySessionRepository;
		this.userActivitySessionUtil = userActivitySessionUtil;
		this.rabbitTemplate = rabbitTemplate;
		this.eventLogRepository = eventLogRepository;
		this.sentryService = sentryService;
	}

	Logger logger = LoggerFactory.getLogger(UserActivitySessionServiceImpl.class);
	@Value("${rabbitmq.exchange.name}")
	private String exchange;

	@Value("${rabbitmq.routing.key}")
	private String routingKey;
	@Value("${add.event.db}")
	private Boolean addEventInDb;

	// Circuit breaker pattern for RabbitMQ
	private final AtomicBoolean rabbitMqCircuitOpen = new AtomicBoolean(false);
	private final AtomicInteger failureCount = new AtomicInteger(0);
	private static final int FAILURE_THRESHOLD = 5; // Number of failures before opening circuit
	private static final long CIRCUIT_RESET_TIMEOUT = 60000; // 60 seconds before trying again
	private volatile long circuitOpenTime = 0;



	/**
	 * Checks if the RabbitMQ circuit breaker is open (i.e., if we should avoid sending messages)
	 *
	 * @return true if the circuit is open and we should avoid sending messages
	 */
	private boolean isCircuitOpen() {
		if (rabbitMqCircuitOpen.get()) {
			// Check if it's time to try again
			if (System.currentTimeMillis() - circuitOpenTime > CIRCUIT_RESET_TIMEOUT) {
				logger.info("RabbitMQ circuit breaker reset after timeout period");
				rabbitMqCircuitOpen.set(false);
				failureCount.set(0);
				return false;
			}
			return true;
		}
		return false;
	}

	/**
	 * Records a successful RabbitMQ operation and resets the failure count
	 */
	private void recordSuccess() {
		failureCount.set(0);
	}

	/**
	 * Records a failed RabbitMQ operation and potentially opens the circuit
	 *
	 * @param ex The exception that occurred
	 */
	private void recordFailure(Exception ex) {
		int currentFailures = failureCount.incrementAndGet();
		logger.warn("RabbitMQ operation failed ({} consecutive failures): {}", currentFailures, ex.getMessage());

		if (currentFailures >= FAILURE_THRESHOLD) {
			logger.error("RabbitMQ circuit breaker opened after {} consecutive failures", FAILURE_THRESHOLD);
			rabbitMqCircuitOpen.set(true);
			circuitOpenTime = System.currentTimeMillis();

			// Send alert to Sentry
			sentryService.withScope(scope -> {
				scope.setExtra("failureCount", String.valueOf(currentFailures));
				scope.setExtra("circuitOpenTime", new java.util.Date(circuitOpenTime).toString());
				scope.setExtra("resetTimeout", CIRCUIT_RESET_TIMEOUT + " ms");
				scope.captureMessage("RabbitMQ circuit breaker opened due to consecutive failures");
			});
		}
	}

	/**
	 * Verifies that the exchange and queue exist and are properly bound
	 */

	@Override
	public ResponseDTO<?> addUserActivitySession(UserActivitySessionDTO userActivitySessionDTO)
			throws ResourceNotFound {
		logger.debug("addUserActivitySession method starts for user: {}", userActivitySessionDTO.getUuid());

		try {
			// Step 1: Convert DTO to entity
			logger.debug("Converting DTO to UserActivitySession entity for user: {}", userActivitySessionDTO.getUuid());
			UserActivitySession entity = userActivitySessionUtil.convertDtoTOUserActivitySession(userActivitySessionDTO);

			// Step 2: Save to database
			logger.debug("Saving UserActivitySession entity to database for user: {}", userActivitySessionDTO.getUuid());
			UserActivitySession userActivitySession = userActivitySessionRepository.save(entity);

			logger.debug("User activity session stored successfully. Session ID: {}", userActivitySession.getId());
			return new ResponseDTO<>(HttpStatus.OK.toString(), "Data Stored Successfully", userActivitySession.getId());

		} catch (Exception e) {
			sentryService.captureException(e);
			logger.error("Error storing user activity session for user: {}", userActivitySessionDTO.getUuid(), e);
			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "Error storing data", null);
		}
	}

	@Override
	public ResponseDTO<?> addUserEventsDbOrQueue(UserActivityEventsDTO userActivityEventsDTO) {
		logger.debug("addUserEventsDbOrQueue method start for user: {}",
				userActivityEventsDTO.getUserActivitySessionId());

		final boolean saveToDb = Boolean.TRUE.equals(addEventInDb);

		try {
			// This method will run in a virtual thread
			if (saveToDb) {
				// Step 1: Convert DTO to EventLog
				logger.debug("Converting DTO to EventLog for user: {}",
						userActivityEventsDTO.getUserActivitySessionId());
				EventLog eventLog = userActivitySessionUtil.convertDtoToEventLog(userActivityEventsDTO);

				// Step 2: Save to database
				logger.debug("Saving user event to database for user: {}",
						userActivityEventsDTO.getUserActivitySessionId());
				eventLogRepository.save(eventLog);

				logger.debug("Event saved to database successfully");
			} else {
				// Check if circuit breaker is open
				if (isCircuitOpen()) {
					// Circuit is open, fall back to database
					logger.warn("RabbitMQ circuit breaker is open, falling back to database storage for user: {}",
							userActivityEventsDTO.getUserActivitySessionId());

					// Convert DTO to EventLog
					EventLog eventLog = userActivitySessionUtil.convertDtoToEventLog(userActivityEventsDTO);

					// Save to database as fallback
					eventLogRepository.save(eventLog);

					logger.info("Successfully saved message to database as fallback for user: {}",
							userActivityEventsDTO.getUserActivitySessionId());
				} else {
					try {
						logger.info(
								"Attempting to publish user event to queue - Exchange: {}, RoutingKey: {}, SessionId: {}",
								exchange, routingKey, userActivityEventsDTO.getUserActivitySessionId());

						// Log the message content for debugging
						logger.debug("Message content: {}", userActivityEventsDTO);

						// Send the message to RabbitMQ with retry logic
						int retryCount = 0;
						int maxRetries = 2;
						boolean sent = false;

						// Direct attempt to send without checking connection first
						while (!sent && retryCount < maxRetries) {
							try {
								// Just try to send - don't check connection first
								rabbitTemplate.convertAndSend(exchange, routingKey, userActivityEventsDTO);
								sent = true;

								// Record successful operation
								recordSuccess();

								logger.info("Successfully published message to RabbitMQ for user: {}",
										userActivityEventsDTO.getUserActivitySessionId());
							} catch (Exception e) {
								retryCount++;
								if (retryCount >= maxRetries) {
									throw e; // Re-throw if we've exhausted retries
								}

								logger.warn("RabbitMQ publish failed, retrying ({}/{}): {}",
										retryCount, maxRetries, e.getMessage());

								// Exponential backoff using virtual thread-friendly approach
								try {
									// Use java.util.concurrent.TimeUnit which is more virtual thread friendly
									java.util.concurrent.TimeUnit.MILLISECONDS.sleep(100 * (long)Math.pow(2, retryCount));
								} catch (InterruptedException ie) {
									Thread.currentThread().interrupt();
								}
							}
						}
					} catch (Exception ex) {
						// Record failure and potentially open circuit
						recordFailure(ex);

						sentryService.captureException(ex);
						logger.error(
								"Failed to publish message to RabbitMQ - Exchange: {}, RoutingKey: {}, Error: {}",
								exchange, routingKey, ex.getMessage(), ex);

						// Try to save to database as fallback
						logger.info("Attempting to save to database as fallback for user: {}",
								userActivityEventsDTO.getUserActivitySessionId());

						EventLog eventLog = userActivitySessionUtil.convertDtoToEventLog(userActivityEventsDTO);
						eventLogRepository.save(eventLog);

						logger.info("Successfully saved to database as fallback for user: {}",
								userActivityEventsDTO.getUserActivitySessionId());
					}
				}
			}

			logger.debug("addUserEventsDbOrQueue method ends - processing completed");
			return new ResponseDTO<>(HttpStatus.OK.toString(), "User event processed successfully", null);

		} catch (Exception e) {
			sentryService.captureException(e);
			logger.error("Error processing user event", e);
			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
				"Error processing user event: " + e.getMessage(), null);
		}
	}

	@Override
	public ResponseDTO<?> getUserSessionWithFilters(PaginationRequestDTO paginationRequestDTO) {
		logger.debug("getUserSessionWithFilters method start with page: {}, size: {}, search: {}",
				paginationRequestDTO.getPage(), paginationRequestDTO.getSize(), paginationRequestDTO.getSearch());

		try {
			// Step 1: Prepare parameters
			Pageable pageable = PageRequest.of(paginationRequestDTO.getPage(), paginationRequestDTO.getSize(), Sort.by(Sort.Direction.DESC, "createdAt"));
			LocalDateTime fromTime = paginationRequestDTO.getFrom() != null ? paginationRequestDTO.getFrom().atStartOfDay() : null;
			LocalDateTime toTime = paginationRequestDTO.getTo() != null ? paginationRequestDTO.getTo().atTime(LocalTime.MAX) : null;

			// Step 2: Query database
			Page<UserActivitySessionDTO> userActivitySessionPage = userActivitySessionRepository.filteredUserSession(
					paginationRequestDTO.getSearch(), fromTime, toTime, pageable);

			// Check if page is null
			if (userActivitySessionPage == null) {
				logger.warn(NULL_PAGE_WARNING);
				return new ResponseDTO<>(HttpStatus.OK.toString(), NO_SESSIONS_FOUND,
						new PaginatedResult<>(List.of(), 0));
			}

			// Get all sessions from the page
			List<UserActivitySessionDTO> sessions = userActivitySessionPage.getContent();

			// Check if content is empty but total elements exist
			if (sessions.isEmpty()) {
				if (userActivitySessionPage.getTotalElements() == 0) {
					logger.debug("No sessions found matching the search criteria");
					return new ResponseDTO<>(HttpStatus.OK.toString(), NO_SESSIONS_FOUND,
							new PaginatedResult<>(List.of(), 0));
				} else {
					logger.debug("Requested page {} is beyond available data. Total elements: {}, Total pages: {}",
							paginationRequestDTO.getPage(), userActivitySessionPage.getTotalElements(), userActivitySessionPage.getTotalPages());
					return new ResponseDTO<>(HttpStatus.OK.toString(), "Requested page is beyond available data",
							new PaginatedResult<>(List.of(), userActivitySessionPage.getTotalElements()));
				}
			}

			// Step 3: Prepare response
			PaginatedResult<UserActivitySessionDTO> result = new PaginatedResult<>(
					sessions,
					userActivitySessionPage.getTotalElements());

			logger.debug("getUserSessionWithFilters method ends - total sessions fetched: {}",
					userActivitySessionPage.getTotalElements());

			return new ResponseDTO<>(HttpStatus.OK.toString(), "Pagination for session run successfully", result);
		} catch (Exception e) {
			sentryService.captureException(e);
			logger.error(ERROR_FETCHING_SESSIONS, e);
			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(), ERROR_FETCHING_SESSIONS, null);
		}
	}

	@Override
	public ResponseDTO<?> getUserEventsWithFilters(PaginationRequestDTO paginationRequestDTO, String sessionId) {
		logger.debug("getUserEventsWithFilters method starts");

		try {
			// Step 1: Validate data and prepare parameters
			UserActivitySessionUtil.validatePaginationData(paginationRequestDTO, sessionId);

			Pageable pageable = PageRequest.of(paginationRequestDTO.getPage(), paginationRequestDTO.getSize(), Sort.by(Sort.Direction.DESC, "createdAt"));
			LocalDateTime fromTime = paginationRequestDTO.getFrom() != null ? paginationRequestDTO.getFrom().atStartOfDay() : null;
			LocalDateTime toTime = paginationRequestDTO.getTo() != null ? paginationRequestDTO.getTo().atTime(LocalTime.MAX) : null;

			// Step 2: Query database
			Page<EventLog> eventLogPage = eventLogRepository.searchBySessionIdAndFields(
				sessionId,
				paginationRequestDTO.getSearch(),
				fromTime,
				toTime,
				pageable);

			// Check if page is null
			if (eventLogPage == null) {
				logger.warn(NULL_PAGE_WARNING);
				return new ResponseDTO<>(HttpStatus.OK.toString(), NO_EVENTS_FOUND,
						new PaginatedResult<>(List.of(), 0));
			}

			// Get all events from the page
			List<EventLog> events = eventLogPage.getContent();

			// Check if content is empty but total elements exist
			if (events.isEmpty()) {
				if (eventLogPage.getTotalElements() == 0) {
					logger.debug("No events found matching the search criteria");
					return new ResponseDTO<>(HttpStatus.OK.toString(), NO_EVENTS_FOUND,
							new PaginatedResult<>(List.of(), 0));
				} else {
					logger.debug("Requested page {} is beyond available data. Total elements: {}, Total pages: {}",
							paginationRequestDTO.getPage(), eventLogPage.getTotalElements(), eventLogPage.getTotalPages());
					return new ResponseDTO<>(HttpStatus.OK.toString(), "Requested page is beyond available data",
							new PaginatedResult<>(List.of(), eventLogPage.getTotalElements()));
				}
			}

			// Step 3: Transform results
			List<UserActivityEventsDTO> userActivityEventsList = events.stream()
				.map(userActivitySessionUtil::convertEventLogToDto)
				.toList();

			// Step 4: Create response
			PaginatedResult<UserActivityEventsDTO> result = new PaginatedResult<>(userActivityEventsList, eventLogPage.getTotalElements());

			logger.debug("getUserEventsWithFilters method ends - total events fetched: {}", eventLogPage.getTotalElements());
			return new ResponseDTO<>(HttpStatus.OK.toString(), "Pagination for events run successfully", result);
		} catch (Exception e) {
			sentryService.captureException(e);
			logger.error("Error fetching user events for session: {}", sessionId, e);
			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(), ERROR_FETCHING_EVENTS, null);
		}
	}

	@Override
	public ResponseDTO<?> getEvents(PaginationRequestDTO paginationRequestDTO) {
		logger.debug(
				"getEvents method starts with page: {}, size: {}, search: {}, module: {}, action: {}, systemName: {}",
				paginationRequestDTO.getPage(), paginationRequestDTO.getSize(), paginationRequestDTO.getSearch(),
				paginationRequestDTO.getModule(), paginationRequestDTO.getAction(),
				paginationRequestDTO.getSystemName());

		try {
			// Step 1: Prepare parameters and build specification
			Pageable pageable = PageRequest.of(paginationRequestDTO.getPage(), paginationRequestDTO.getSize(), Sort.by(Sort.Direction.DESC, "createdAt"));
			LocalDateTime fromTime = paginationRequestDTO.getFrom() != null ? paginationRequestDTO.getFrom().atStartOfDay() : null;
			LocalDateTime toTime = paginationRequestDTO.getTo() != null ? paginationRequestDTO.getTo().atTime(LocalTime.MAX) : null;

			Specification<EventLog> eventLogSpecification = Specification
				.where(EventLogSpecification.filterBySearch(paginationRequestDTO.getSearch()))
				.and(EventLogSpecification.filterByModule(paginationRequestDTO.getModule()))
				.and(EventLogSpecification.filterByAction(paginationRequestDTO.getAction()))
				.and(EventLogSpecification.filterBySystemName(paginationRequestDTO.getSystemName()))
				.and(EventLogSpecification.filterByTimeRange(fromTime, toTime));

			// Step 2: Query database
			Page<EventLog> eventLogPage = eventLogRepository.findAll(eventLogSpecification, pageable);

			// Check if page is null
			if (eventLogPage == null) {
				logger.warn(NULL_PAGE_WARNING);
				return new ResponseDTO<>(HttpStatus.OK.toString(), NO_EVENTS_FOUND,
						new PaginatedResult<>(List.of(), 0));
			}

			// Get all events from the page
			List<EventLog> events = eventLogPage.getContent();

			// Check if content is empty but total elements exist
			if (events.isEmpty()) {
				if (eventLogPage.getTotalElements() == 0) {
					logger.debug("No events found matching the search criteria");
					return new ResponseDTO<>(HttpStatus.OK.toString(), NO_EVENTS_FOUND,
							new PaginatedResult<>(List.of(), 0));
				} else {
					logger.debug("Requested page {} is beyond available data. Total elements: {}, Total pages: {}",
							paginationRequestDTO.getPage(), eventLogPage.getTotalElements(), eventLogPage.getTotalPages());
					return new ResponseDTO<>(HttpStatus.OK.toString(), "Requested page is beyond available data",
							new PaginatedResult<>(List.of(), eventLogPage.getTotalElements()));
				}
			}

			// Step 3: Transform results
			List<UserActivityEventsDTO> userActivityEventsList = events.stream()
				.map(userActivitySessionUtil::convertEventLogToDto)
				.toList();

			// Step 4: Create response
			PaginatedResult<UserActivityEventsDTO> result = new PaginatedResult<>(userActivityEventsList, eventLogPage.getTotalElements());

			logger.debug("getEvents method ends - total events fetched: {}", eventLogPage.getTotalElements());
			return new ResponseDTO<>(HttpStatus.OK.toString(), "Pagination for events run successfully", result);
		} catch (Exception e) {
			sentryService.captureException(e);
			logger.error(ERROR_FETCHING_EVENTS, e);
			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(), ERROR_FETCHING_EVENTS, null);
		}
	}

	@Override
	public ResponseDTO<?> getUserSessions(SessionSearchRequestDTO requestDto) {
		logger.debug(
				"getUserSessions method starts with page: {}, size: {}, search: {}, systemName: {}, email: {}, name: {}",
				requestDto.getPage(), requestDto.getSize(), requestDto.getSearch(), requestDto.getSystemName(),
				requestDto.getEmail(), requestDto.getName());

		try {
			Page<UserActivitySession> page = searchSessions(requestDto);

			// Check if page is null
			if (page == null) {
				logger.warn(NULL_PAGE_WARNING);
				return new ResponseDTO<>(HttpStatus.OK.toString(), NO_SESSIONS_FOUND,
						new UserActivitySessionPaginatedDTO(0, List.of()));
			}

			// Get all sessions from the page
			List<UserActivitySession> sessions = page.getContent();

			// Check if content is empty but total elements exist
			if (sessions.isEmpty()) {
				if (page.getTotalElements() == 0) {
					logger.debug("No sessions found matching the search criteria");
					return new ResponseDTO<>(HttpStatus.OK.toString(), NO_SESSIONS_FOUND,
							new UserActivitySessionPaginatedDTO(0, List.of()));
				} else {
					logger.debug("Requested page {} is beyond available data. Total elements: {}, Total pages: {}",
							requestDto.getPage(), page.getTotalElements(), page.getTotalPages());
					return new ResponseDTO<>(HttpStatus.OK.toString(), "Requested page is beyond available data",
							new UserActivitySessionPaginatedDTO(page.getTotalElements(), List.of()));
				}
			}

			List<UserActivitySessionResponseDTO> dtoList = sessions.stream()
					.map(UserActivitySessionResponseDTO::new).toList();

			UserActivitySessionPaginatedDTO paginatedDTO = new UserActivitySessionPaginatedDTO(page.getTotalElements(),
					dtoList);

			logger.debug("getUserSessions method ends - total sessions fetched: {}", page.getTotalElements());
			return new ResponseDTO<>(HttpStatus.OK.toString(), "Pagination for sessions run successfully",
					paginatedDTO);
		} catch (Exception e) {
			sentryService.captureException(e);
			logger.error(ERROR_FETCHING_SESSIONS, e);
			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(), ERROR_FETCHING_SESSIONS, null);
		}
	}

	public Page<UserActivitySession> searchSessions(SessionSearchRequestDTO dto) {
		Pageable pageable = PageRequest.of(dto.getPage(), dto.getSize(), Sort.by(Sort.Direction.DESC, "createdAt"));
		return userActivitySessionRepository.findAll(UserActivitySessionPaginationSpecification.fromDTO(dto), pageable);
	}

	/**
	 * Example method showing how to use CompletableFuture with virtual threads.
	 * This method demonstrates how to run a task asynchronously using virtual threads.
	 *
	 * @param userActivityEventsDTO The DTO containing user activity events
	 * @return A CompletableFuture that will complete with the result of processing the events
	 */
	@Override
	public CompletableFuture<ResponseDTO<?>> processEventsAsync(UserActivityEventsDTO userActivityEventsDTO) {
		return CompletableFuture.supplyAsync(() -> {
			// This code runs in a virtual thread
			logger.info("Processing events asynchronously in virtual thread: {}", Thread.currentThread());
			return addUserEventsDbOrQueue(userActivityEventsDTO);
		});
	}
//
//	@Override
//	public ResponseDTO<?> testRabbitMQConnection() {
//		logger.info("Testing RabbitMQ connection...");
//
//		// Check if circuit breaker is open
//		if (isCircuitOpen()) {
//			logger.warn("RabbitMQ circuit breaker is open, cannot test connection");
//			return new ResponseDTO<>(HttpStatus.SERVICE_UNAVAILABLE.toString(),
//					"RabbitMQ circuit breaker is open. Please try again later. " +
//					"Circuit will reset after " + (CIRCUIT_RESET_TIMEOUT / 1000) + " seconds of timeout.", null);
//		}
//
//		try {
//			// Try to send a test message with retry logic - don't check connection first
//			String testMessage = "Test message sent at " + java.time.LocalDateTime.now();
//			logger.info("Sending test message to RabbitMQ - Exchange: {}, RoutingKey: {}, Message: {}", exchange,
//					routingKey, testMessage);
//
//			int retryCount = 0;
//			int maxRetries = 3;
//			boolean sent = false;
//			Exception lastException = null;
//
//			while (!sent && retryCount < maxRetries) {
//				try {
//					rabbitTemplate.convertAndSend(exchange, routingKey, testMessage);
//					sent = true;
//
//					// Record successful operation
//					recordSuccess();
//
//					logger.info("Test message sent successfully to RabbitMQ");
//					return new ResponseDTO<>(HttpStatus.OK.toString(),
//							"RabbitMQ connection test successful. Message sent to exchange '" + exchange
//									+ "' with routing key '" + routingKey + "'",
//							testMessage);
//				} catch (Exception e) {
//					lastException = e;
//					retryCount++;
//
//					logger.warn("RabbitMQ test message failed, retrying ({}/{}): {}",
//							retryCount, maxRetries, e.getMessage());
//
//					// Exponential backoff using virtual thread-friendly approach
//					try {
//						// Use java.util.concurrent.TimeUnit which is more virtual thread friendly
//						java.util.concurrent.TimeUnit.MILLISECONDS.sleep(100 * (long)Math.pow(2, retryCount));
//					} catch (InterruptedException ie) {
//						Thread.currentThread().interrupt();
//					}
//				}
//			}
//
//			// If we get here, all retries failed
//			if (lastException != null) {
//				// Record failure and potentially open circuit
//				recordFailure(lastException);
//
//				Sentry.captureException(lastException);
//				logger.error("RabbitMQ connection test failed after {} retries: {}",
//						maxRetries, lastException.getMessage(), lastException);
//				return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
//						"RabbitMQ connection test failed after " + maxRetries + " retries: " + lastException.getMessage(), null);
//			}
//
//			// This should never happen, but just in case
//			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
//					"RabbitMQ connection test failed for unknown reason", null);
//
//		} catch (Exception e) {
//			// Record failure and potentially open circuit
//			recordFailure(e);
//
//			Sentry.captureException(e);
//			logger.error("RabbitMQ connection test failed: {}", e.getMessage(), e);
//			return new ResponseDTO<>(HttpStatus.INTERNAL_SERVER_ERROR.toString(),
//					"RabbitMQ connection test failed: " + e.getMessage(), null);
//		}
//	}
//


}