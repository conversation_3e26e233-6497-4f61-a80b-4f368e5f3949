{"timestamp": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "timeZone": "IST"}}, "level": {"$resolver": "level", "field": "name"}, "thread": {"$resolver": "thread", "field": "name"}, "logger": {"$resolver": "logger", "field": "name"}, "message": {"$resolver": "message", "stringified": true}, "exception": {"$resolver": "exception", "field": "stackTrace"}, "trace_id": {"$resolver": "mdc", "key": "trace_id"}, "span_id": {"$resolver": "mdc", "key": "span_id"}, "service": {"name": "${service.name}"}}