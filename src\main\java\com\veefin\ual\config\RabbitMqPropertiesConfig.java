package com.veefin.ual.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * Configuration class to load RabbitMQ specific properties
 * This helps separate RabbitMQ configuration from the main application properties
 */
@Configuration
@PropertySource("classpath:rabbitmq.properties")
public class RabbitMqPropertiesConfig {
    // This class is intentionally empty
    // It serves as a marker to load the rabbitmq.properties file
}
