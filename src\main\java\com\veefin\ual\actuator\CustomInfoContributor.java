package com.veefin.ual.actuator;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.stereotype.Component;

/**
 * Custom info contributor to add additional application information
 */
@Component
public class CustomInfoContributor implements InfoContributor {

    @Value("${spring.application.name:ual}")
    private String applicationName;

    @Value("${spring.profiles.active:none}")
    private String activeProfile;

    @Override
    public void contribute(Info.Builder builder) {
        Map<String, Object> details = new HashMap<>();

        // Add environment details
        Map<String, Object> environmentDetails = new HashMap<>();
        environmentDetails.put("profile", activeProfile);
        details.put("environment", environmentDetails);

        // Add application details
        Map<String, Object> appDetails = new HashMap<>();
        appDetails.put("name", applicationName);
        appDetails.put("type", "Spring Boot Application");
        details.put("application", appDetails);

        // Add custom details
        builder.withDetails(details);
    }
}
